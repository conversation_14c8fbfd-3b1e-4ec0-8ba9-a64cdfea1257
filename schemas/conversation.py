"""Conversation schemas for request/response validation."""

from datetime import datetime
from pydantic import BaseModel
from typing import Optional


class ConversationCreate(BaseModel):
    """Conversation creation request schema."""

    title: Optional[str] = "新的对话"
    
    model_config = {"from_attributes": True}


class ConversationResponse(BaseModel):
    """Conversation response schema."""

    id: int
    title: str
    sticky_flag: bool = False
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class ConversationUpdateRequest(BaseModel):
    """Conversation update request schema."""

    sticky_flag: Optional[bool] = None
    
    model_config = {"from_attributes": True}