#!/usr/bin/env python3
"""
测试Content-Type问题的脚本
"""

import requests
import json

BASE_URL = "http://localhost:8001"

def test_login():
    """测试登录获取token"""
    print("1. 测试登录...")
    
    login_data = {
        "username": "test_user",
        "password": "test_password"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/login", json=login_data)
        print(f"   登录响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                token = result['data']['access_token']
                print(f"   ✅ 登录成功")
                return token
            else:
                print(f"   ❌ 登录失败: {result.get('message')}")
        else:
            print(f"   ❌ 登录请求失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 登录异常: {e}")
    
    return None

def test_content_type_variations(token):
    """测试不同的Content-Type设置"""
    print("\n2. 测试不同的Content-Type设置...")
    
    test_data = {"title": "测试对话"}
    
    test_cases = [
        {
            "name": "application/json",
            "headers": {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            },
            "method": "json"
        },
        {
            "name": "application/json; charset=utf-8",
            "headers": {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json; charset=utf-8"
            },
            "method": "json"
        },
        {
            "name": "手动JSON字符串",
            "headers": {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json; charset=utf-8"
            },
            "method": "data"
        },
        {
            "name": "不设置Content-Type",
            "headers": {
                "Authorization": f"Bearer {token}"
            },
            "method": "json"
        }
    ]
    
    for case in test_cases:
        print(f"\n   测试: {case['name']}")
        
        try:
            if case['method'] == 'json':
                # 使用requests的json参数
                response = requests.post(
                    f"{BASE_URL}/api/conversations/new",
                    json=test_data,
                    headers=case['headers']
                )
            else:
                # 手动构造JSON字符串
                json_str = json.dumps(test_data, ensure_ascii=False)
                response = requests.post(
                    f"{BASE_URL}/api/conversations/new",
                    data=json_str.encode('utf-8'),
                    headers=case['headers']
                )
            
            print(f"      请求Content-Type: {response.request.headers.get('Content-Type', 'None')}")
            print(f"      响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 200:
                    print(f"      ✅ 成功: {result['data']['title']}")
                else:
                    print(f"      ❌ 业务失败: {result.get('message')}")
            else:
                print(f"      ❌ 请求失败: {response.text}")
                
        except Exception as e:
            print(f"      ❌ 异常: {e}")

def main():
    """主测试函数"""
    print("=== Content-Type 测试 ===")
    
    # 1. 登录获取token
    token = test_login()
    if not token:
        print("无法获取token，终止测试")
        return
    
    # 2. 测试不同的Content-Type设置
    test_content_type_variations(token)
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
