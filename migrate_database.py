#!/usr/bin/env python3
"""
数据库迁移脚本 - 添加新字段
"""

import sqlite3
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_database():
    """执行数据库迁移"""
    db_path = "database.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        logger.info("开始数据库迁移...")
        
        # 检查conversation表是否已有新字段
        cursor.execute("PRAGMA table_info(conversation)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # 添加conversation表的新字段
        if 'sticky_flag' not in columns:
            logger.info("添加conversation.sticky_flag字段...")
            cursor.execute("ALTER TABLE conversation ADD COLUMN sticky_flag BOOLEAN DEFAULT FALSE")
            
        if 'updated_at' not in columns:
            logger.info("添加conversation.updated_at字段...")
            cursor.execute("ALTER TABLE conversation ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
            # 将现有记录的updated_at设置为created_at的值
            cursor.execute("UPDATE conversation SET updated_at = created_at WHERE updated_at IS NULL")
        
        # 检查message表是否已有新字段
        cursor.execute("PRAGMA table_info(message)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # 添加message表的新字段
        if 'parent_msg_id' not in columns:
            logger.info("添加message.parent_msg_id字段...")
            cursor.execute("ALTER TABLE message ADD COLUMN parent_msg_id INTEGER DEFAULT 0")
            
        if 'created_at' not in columns:
            logger.info("添加message.created_at字段...")
            cursor.execute("ALTER TABLE message ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
            # 将现有记录的created_at设置为timestamp的值
            cursor.execute("UPDATE message SET created_at = timestamp WHERE created_at IS NULL")
            
        if 'updated_at' not in columns:
            logger.info("添加message.updated_at字段...")
            cursor.execute("ALTER TABLE message ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
            # 将现有记录的updated_at设置为timestamp的值
            cursor.execute("UPDATE message SET updated_at = timestamp WHERE updated_at IS NULL")
        
        # 提交更改
        conn.commit()
        logger.info("数据库迁移完成!")
        
        # 显示表结构
        logger.info("当前conversation表结构:")
        cursor.execute("PRAGMA table_info(conversation)")
        for column in cursor.fetchall():
            logger.info(f"  {column[1]} {column[2]} {'NOT NULL' if column[3] else 'NULL'} {'DEFAULT ' + str(column[4]) if column[4] else ''}")
            
        logger.info("当前message表结构:")
        cursor.execute("PRAGMA table_info(message)")
        for column in cursor.fetchall():
            logger.info(f"  {column[1]} {column[2]} {'NOT NULL' if column[3] else 'NULL'} {'DEFAULT ' + str(column[4]) if column[4] else ''}")
        
    except Exception as e:
        logger.error(f"数据库迁移失败: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    migrate_database()
