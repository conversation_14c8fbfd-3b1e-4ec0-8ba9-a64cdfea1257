#!/usr/bin/env python3
"""
前端优化功能测试脚本
验证前端与后端API的集成是否正常工作
"""

import sys
import os
import asyncio
import json
from datetime import datetime

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fastapi.testclient import TestClient
from main import app
from crud.database import SessionLocal
from crud.conversation_dao import ConversationDao
from crud.message_dao import MessageDao
from services.auth_service import AuthService
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建测试客户端
client = TestClient(app)

def test_login_and_get_token():
    """测试登录并获取token"""
    logger.info("测试用户登录...")
    
    # 这里使用测试用户，实际环境中需要有效的LDAP用户
    login_data = {
        "username": "test_user",
        "password": "test_password"
    }
    
    try:
        response = client.post("/api/login", json=login_data)
        if response.status_code == 200:
            data = response.json()
            logger.info("✓ 登录成功")
            logger.info(f"Response data: {data}")
            return data.get("access_token") or data.get("data", {}).get("access_token")
        else:
            logger.warning(f"登录失败: {response.status_code} - {response.text}")
            # 为了测试，我们手动生成一个token
            db = SessionLocal()
            try:
                token_data = AuthService.generate_token("test_user")
                logger.info("✓ 使用测试token")
                return token_data.access_token
            except Exception as e:
                logger.error(f"生成测试token失败: {e}")
                return None
            finally:
                db.close()
    except Exception as e:
        logger.error(f"登录测试失败: {e}")
        return None

def test_conversations_api(token):
    """测试对话相关API"""
    logger.info("测试对话API...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # 1. 创建新对话
        logger.info("1. 测试创建对话...")
        create_response = client.post(
            "/api/conversations/new",
            json={"title": "测试对话"},
            headers=headers
        )
        
        if create_response.status_code == 200:
            conversation_data = create_response.json()
            conversation_id = conversation_data["data"]["id"]
            logger.info(f"✓ 创建对话成功: {conversation_id}")
            
            # 2. 获取对话列表
            logger.info("2. 测试获取对话列表...")
            list_response = client.get("/api/conversations", headers=headers)
            
            if list_response.status_code == 200:
                conversations = list_response.json()
                logger.info(f"✓ 获取对话列表成功，共 {len(conversations['data'])} 个对话")
                
                # 验证新字段是否存在
                if conversations["data"]:
                    conv = conversations["data"][0]
                    required_fields = ["id", "title", "sticky_flag", "created_at", "updated_at"]
                    missing_fields = [field for field in required_fields if field not in conv]
                    
                    if not missing_fields:
                        logger.info("✓ 对话数据包含所有必需字段")
                    else:
                        logger.error(f"✗ 对话数据缺少字段: {missing_fields}")
                
                # 3. 测试置顶功能
                logger.info("3. 测试置顶功能...")
                sticky_response = client.put(
                    f"/api/conversations/{conversation_id}/sticky",
                    json={"sticky_flag": True},
                    headers=headers
                )
                
                if sticky_response.status_code == 200:
                    logger.info("✓ 置顶功能测试成功")
                    
                    # 再次获取列表验证置顶状态
                    list_response2 = client.get("/api/conversations", headers=headers)
                    if list_response2.status_code == 200:
                        conversations2 = list_response2.json()
                        updated_conv = next((c for c in conversations2["data"] if c["id"] == conversation_id), None)
                        if updated_conv and updated_conv["sticky_flag"]:
                            logger.info("✓ 置顶状态更新成功")
                        else:
                            logger.error("✗ 置顶状态更新失败")
                else:
                    logger.error(f"✗ 置顶功能测试失败: {sticky_response.status_code}")
            else:
                logger.error(f"✗ 获取对话列表失败: {list_response.status_code}")
        else:
            logger.error(f"✗ 创建对话失败: {create_response.status_code}")
            
    except Exception as e:
        logger.error(f"对话API测试失败: {e}")

def test_messages_api(token):
    """测试消息相关API"""
    logger.info("测试消息API...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # 首先创建一个对话
        create_response = client.post(
            "/api/conversations/new",
            json={"title": "消息测试对话"},
            headers=headers
        )
        
        if create_response.status_code == 200:
            conversation_data = create_response.json()
            conversation_id = conversation_data["data"]["id"]
            
            # 测试获取消息列表
            logger.info("测试获取消息列表...")
            messages_response = client.get(
                f"/api/messages?conversation_id={conversation_id}",
                headers=headers
            )
            
            if messages_response.status_code == 200:
                messages_data = messages_response.json()
                logger.info("✓ 获取消息列表成功")
                
                # 验证消息数据结构
                if messages_data["data"]:
                    messages = messages_data["data"][str(conversation_id)]
                    if messages:
                        msg = messages[0]
                        required_fields = ["id", "parent_msg_id", "role", "content", "timestamp", "created_at", "updated_at"]
                        missing_fields = [field for field in required_fields if field not in msg]
                        
                        if not missing_fields:
                            logger.info("✓ 消息数据包含所有必需字段")
                        else:
                            logger.error(f"✗ 消息数据缺少字段: {missing_fields}")
            else:
                logger.error(f"✗ 获取消息列表失败: {messages_response.status_code}")
        else:
            logger.error(f"✗ 创建测试对话失败: {create_response.status_code}")
            
    except Exception as e:
        logger.error(f"消息API测试失败: {e}")

def test_logout_api(token):
    """测试登出API"""
    logger.info("测试登出API...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = client.post("/api/logout", headers=headers)
        if response.status_code == 200:
            logger.info("✓ 登出API测试成功")
        else:
            logger.error(f"✗ 登出API测试失败: {response.status_code}")
    except Exception as e:
        logger.error(f"登出API测试失败: {e}")

def main():
    """主测试函数"""
    logger.info("开始前端优化API测试...")
    
    try:
        # 1. 测试登录
        token = test_login_and_get_token()
        if not token:
            logger.error("无法获取有效token，跳过API测试")
            return
        
        # 2. 测试对话API
        test_conversations_api(token)
        
        # 3. 测试消息API
        test_messages_api(token)
        
        # 4. 测试登出API
        test_logout_api(token)
        
        logger.info("🎉 前端优化API测试完成！")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
