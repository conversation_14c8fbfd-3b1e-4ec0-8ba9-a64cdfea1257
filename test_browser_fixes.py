#!/usr/bin/env python3
"""
浏览器错误修复验证脚本
验证前端修复是否解决了浏览器报错问题
"""

import sys
import os
import asyncio
import json
from datetime import datetime

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fastapi.testclient import TestClient
from main import app
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建测试客户端
client = TestClient(app)

def test_static_files_accessibility():
    """测试静态文件是否可访问"""
    logger.info("测试静态文件可访问性...")
    
    static_files = [
        '/static/login.html',
        '/static/chat.html',
        '/static/index.html',
        '/static/js/login.js',
        '/static/js/chat.js',
        '/static/js/config.js',
        '/static/js/utils.js',
        '/static/css/styles.css'
    ]
    
    for file_path in static_files:
        try:
            response = client.get(file_path)
            if response.status_code == 200:
                logger.info(f"✓ {file_path} - 可访问")
            else:
                logger.error(f"✗ {file_path} - 状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"✗ {file_path} - 错误: {e}")

def test_html_structure():
    """测试HTML文件结构"""
    logger.info("测试HTML文件结构...")
    
    html_files = [
        ('/static/login.html', ['vue.global.prod.js', 'config.js', 'utils.js']),
        ('/static/chat.html', ['vue.global.prod.js', 'config.js', 'utils.js']),
        ('/static/index.html', ['vue.global.prod.js', 'config.js', 'utils.js'])
    ]
    
    for file_path, required_scripts in html_files:
        try:
            response = client.get(file_path)
            if response.status_code == 200:
                content = response.text
                
                # 检查是否使用生产版本的Vue
                if 'vue.global.prod.js' in content:
                    logger.info(f"✓ {file_path} - 使用生产版本Vue")
                else:
                    logger.error(f"✗ {file_path} - 未使用生产版本Vue")
                
                # 检查必需的脚本文件
                for script in required_scripts:
                    if script in content:
                        logger.info(f"✓ {file_path} - 包含 {script}")
                    else:
                        logger.error(f"✗ {file_path} - 缺少 {script}")
                        
                # 检查是否避免了开发版本的CDN警告
                if 'cdn.tailwindcss.com' in content and '3.4.0' in content:
                    logger.info(f"✓ {file_path} - 使用指定版本的Tailwind")
                elif 'cdn.tailwindcss.com' in content:
                    logger.warn(f"⚠ {file_path} - 使用Tailwind CDN但未指定版本")
                    
        except Exception as e:
            logger.error(f"✗ {file_path} - 错误: {e}")

def test_javascript_syntax():
    """测试JavaScript文件语法"""
    logger.info("测试JavaScript文件语法...")
    
    js_files = [
        '/static/js/login.js',
        '/static/js/chat.js',
        '/static/js/config.js',
        '/static/js/utils.js'
    ]
    
    for file_path in js_files:
        try:
            response = client.get(file_path)
            if response.status_code == 200:
                content = response.text
                
                # 基本语法检查
                if 'const ' in content or 'let ' in content or 'var ' in content:
                    logger.info(f"✓ {file_path} - 包含变量声明")
                
                # 检查是否有明显的语法错误
                if content.count('{') == content.count('}'):
                    logger.info(f"✓ {file_path} - 大括号匹配")
                else:
                    logger.error(f"✗ {file_path} - 大括号不匹配")
                
                if content.count('(') == content.count(')'):
                    logger.info(f"✓ {file_path} - 小括号匹配")
                else:
                    logger.error(f"✗ {file_path} - 小括号不匹配")
                    
        except Exception as e:
            logger.error(f"✗ {file_path} - 错误: {e}")

def test_config_functionality():
    """测试配置文件功能"""
    logger.info("测试配置文件功能...")
    
    try:
        response = client.get('/static/js/config.js')
        if response.status_code == 200:
            content = response.text
            
            # 检查配置对象
            if 'window.AppConfig' in content:
                logger.info("✓ config.js - 导出AppConfig到全局")
            else:
                logger.error("✗ config.js - 未导出AppConfig")
                
            # 检查环境检测
            if 'isProduction' in content:
                logger.info("✓ config.js - 包含环境检测")
            else:
                logger.error("✗ config.js - 缺少环境检测")
                
            # 检查配置项
            required_configs = ['api', 'ui', 'features', 'cache']
            for config in required_configs:
                if config in content:
                    logger.info(f"✓ config.js - 包含 {config} 配置")
                else:
                    logger.error(f"✗ config.js - 缺少 {config} 配置")
                    
    except Exception as e:
        logger.error(f"config.js 测试失败: {e}")

def test_utils_functionality():
    """测试工具函数功能"""
    logger.info("测试工具函数功能...")
    
    try:
        response = client.get('/static/js/utils.js')
        if response.status_code == 200:
            content = response.text
            
            # 检查工具对象导出
            required_utils = ['ErrorHandler', 'DebugUtils', 'FormatUtils', 'ApiUtils', 'PerformanceUtils']
            for util in required_utils:
                if f'window.{util}' in content:
                    logger.info(f"✓ utils.js - 导出 {util}")
                else:
                    logger.error(f"✗ utils.js - 未导出 {util}")
                    
            # 检查错误处理
            if 'setupGlobalErrorHandler' in content:
                logger.info("✓ utils.js - 包含全局错误处理")
            else:
                logger.error("✗ utils.js - 缺少全局错误处理")
                
            # 检查API工具
            if 'fetchWithRetry' in content:
                logger.info("✓ utils.js - 包含重试机制")
            else:
                logger.error("✗ utils.js - 缺少重试机制")
                
    except Exception as e:
        logger.error(f"utils.js 测试失败: {e}")

def test_login_fixes():
    """测试登录页面修复"""
    logger.info("测试登录页面修复...")
    
    try:
        response = client.get('/static/js/login.js')
        if response.status_code == 200:
            content = response.text
            
            # 检查是否使用了新的工具函数
            if 'ApiUtils.fetchWithRetry' in content:
                logger.info("✓ login.js - 使用ApiUtils")
            else:
                logger.error("✗ login.js - 未使用ApiUtils")
                
            if 'DebugUtils.info' in content:
                logger.info("✓ login.js - 使用DebugUtils")
            else:
                logger.error("✗ login.js - 未使用DebugUtils")
                
            if 'AppConfig.cache.tokenKey' in content:
                logger.info("✓ login.js - 使用AppConfig")
            else:
                logger.error("✗ login.js - 未使用AppConfig")
                
            # 检查是否修复了token未定义问题
            if 'token.value' not in content:
                logger.info("✓ login.js - 修复了token未定义问题")
            else:
                logger.error("✗ login.js - 仍存在token未定义问题")
                
    except Exception as e:
        logger.error(f"login.js 测试失败: {e}")

def main():
    """主测试函数"""
    logger.info("开始浏览器错误修复验证...")
    
    try:
        # 1. 测试静态文件可访问性
        test_static_files_accessibility()
        
        # 2. 测试HTML文件结构
        test_html_structure()
        
        # 3. 测试JavaScript语法
        test_javascript_syntax()
        
        # 4. 测试配置文件功能
        test_config_functionality()
        
        # 5. 测试工具函数功能
        test_utils_functionality()
        
        # 6. 测试登录页面修复
        test_login_fixes()
        
        logger.info("🎉 浏览器错误修复验证完成！")
        
    except Exception as e:
        logger.error(f"验证失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
