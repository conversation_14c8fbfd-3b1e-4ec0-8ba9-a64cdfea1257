"""Chat API routes with SSE streaming."""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
import logging
import json

from crud.database import get_database_session
from schemas.message import ChatRequest
from schemas.response import StandardErrorResponse
from services.message_service import MessageService
from utils.dependencies import get_current_username

router = APIRouter(prefix="/api", tags=["聊天对话"])
message_service = MessageService()
logger = logging.getLogger(__name__)


@router.post("/chat/stream", summary="发送消息（SSE流返回）")
async def chat_stream(
    chat_data: ChatRequest,
    current_username: str = Depends(get_current_username),
    db: Session = Depends(get_database_session),
):
    """发送消息并以SSE流的形式返回AI回复"""
    logger.info(
        f"[chat_stream] 用户: {current_username}, 对话ID: {chat_data.conversation_id}, 消息: {chat_data.message}"
    )

    async def generate_response():
        """生成SSE格式的流式响应"""
        try:
            # 1. 验证用户权限并保存用户消息
            user_message = message_service.create_user_message(
                db, chat_data.conversation_id, chat_data.message, current_username, chat_data.parent_msg_id
            )
            logger.info(f"[chat_stream] 用户消息已保存: {user_message}")

            # 2. 生成AI回复并流式返回
            ai_response_content = ""
            references = []
            content_generator, references = message_service.generate_chat_response_with_references(chat_data)

            for chunk in content_generator:
                ai_response_content += chunk
                logger.debug(f"[chat_stream] AI回复流片段: {chunk}")
                # 使用 json.dumps 确保正确的 JSON 转义
                data = json.dumps({
                    "code": 200,
                    "message": "success",
                    "data": {"content": chunk}
                }, ensure_ascii=False)
                yield f'data: {data}\n\n'

            # 3. 保存AI回复到数据库（包含引用内容）
            message_service.create_assistant_message(
                db, chat_data.conversation_id, ai_response_content, user_message.id, references
            )
            logger.info(f"[chat_stream] AI回复已保存, 长度: {len(ai_response_content)}")

            yield "data: [DONE]\n\n"

        except ValueError as e:
            logger.warning(f"[chat_stream] 参数错误: {e}")
            error_data = json.dumps({
                "code": 1001,
                "message": "参数错误",
                "data": {"error": str(e)}
            }, ensure_ascii=False)
            yield f'data: {error_data}\n\n'
            yield "data: [DONE]\n\n"
        except Exception as e:
            logger.error(f"[chat_stream] 处理消息时发生异常: {e}", exc_info=True)
            error_data = json.dumps({
                "code": 1005,
                "message": "内部服务器错误",
                "data": {"error": f"处理消息时发生错误: {str(e)}"}
            }, ensure_ascii=False)
            yield f'data: {error_data}\n\n'
            yield "data: [DONE]\n\n"

    return StreamingResponse(
        generate_response(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Credentials": "true",
        },
    )


@router.get("/chat/stream", summary="发送消息（GET方式，支持EventSource）")
async def chat_stream_get(
    conversation_id: int = Query(..., description="对话ID"),
    message: str = Query(..., description="用户消息"),
    collection_name: str = Query("FinancialResearchOffice", description="知识库集合名称"),
    current_username: str = Depends(get_current_username),
    db: Session = Depends(get_database_session),
):
    """通过GET方式发送消息并以SSE流的形式返回AI回复，支持EventSource"""
    logger.info(
        f"[chat_stream_get] 用户: {current_username}, 对话ID: {conversation_id}, 消息: {message}"
    )

    # 创建ChatRequest对象
    chat_data = ChatRequest(
        conversation_id=conversation_id,
        message=message,
        collection_name=collection_name,
        input={},
        parent_msg_id=0  # GET接口默认为第一条消息
    )

    async def generate_response():
        """生成SSE格式的流式响应"""
        try:
            # 1. 验证用户权限并保存用户消息
            user_message = message_service.create_user_message(
                db, chat_data.conversation_id, chat_data.message, current_username, chat_data.parent_msg_id
            )
            logger.info(f"[chat_stream_get] 用户消息已保存: {user_message}")

            # 2. 生成AI回复并流式返回
            ai_response_content = ""
            references = []
            content_generator, references = message_service.generate_chat_response_with_references(chat_data)

            for chunk in content_generator:
                ai_response_content += chunk
                logger.debug(f"[chat_stream_get] AI回复流片段: {chunk}")
                # 使用 json.dumps 确保正确的 JSON 转义，保持与POST接口一致
                data = json.dumps({
                    "code": 200,
                    "message": "success",
                    "data": {"content": chunk}
                }, ensure_ascii=False)
                yield f'data: {data}\n\n'

            # 3. 保存AI回复到数据库（包含引用内容）
            message_service.create_assistant_message(
                db, chat_data.conversation_id, ai_response_content, user_message.id, references
            )
            logger.info(f"[chat_stream_get] AI回复已保存, 长度: {len(ai_response_content)}")

            yield "data: [DONE]\n\n"

        except ValueError as e:
            logger.warning(f"[chat_stream_get] 参数错误: {e}")
            error_data = json.dumps({
                "code": 1001,
                "message": "参数错误",
                "data": {"error": str(e)}
            }, ensure_ascii=False)
            yield f'data: {error_data}\n\n'
            yield "data: [DONE]\n\n"
        except Exception as e:
            logger.error(f"[chat_stream_get] 处理聊天请求时发生错误: {str(e)}")
            error_data = json.dumps({
                "code": 1005,
                "message": "内部服务器错误",
                "data": {"error": f"处理消息时发生错误: {str(e)}"}
            }, ensure_ascii=False)
            yield f'data: {error_data}\n\n'
            yield "data: [DONE]\n\n"

    return StreamingResponse(
        generate_response(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Credentials": "true",
        },
    )