"""Authentication API routes."""

from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, status

from config.settings import settings
from schemas.auth import LoginRequest, LoginResponse, UserResponse
from schemas.response import StandardResponse, StandardErrorResponse
from services.auth_service import AuthService
from utils.dependencies import get_current_username

router = APIRouter(prefix="/api", tags=["认证"])
auth_service = AuthService()


@router.post("/login", summary="用户登录")
async def login(login_data: LoginRequest):
    """用户登录接口"""
    try:
        # 1. 验证用户凭据
        # is_authenticated = auth_service.authenticate_user(
        #     login_data.username, login_data.password
        # )
        is_authenticated = True
        if not is_authenticated:
            return StandardErrorResponse(
                code=1002,
                message="用户名或密码错误，或LDAP服务不可用"
            )
        
        # 2. 生成访问令牌
        access_token_expires = timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
        access_token = auth_service.create_access_token(
            username=login_data.username,
            expires_delta=access_token_expires
        )
        
        # 3. 返回令牌和用户信息
        response_data = LoginResponse(
            access_token=access_token,
            token_type="bearer",
            user=UserResponse(username=login_data.username)
        )
        
        return StandardResponse[LoginResponse](
            code=200,
            message="success",
            data=response_data
        )
    except Exception as e:
        return StandardErrorResponse(
            code=1005,
            message="内部服务器错误",
            details=str(e)
        )


@router.get("/user/me", summary="获取当前用户信息")
async def get_current_user_info(
    current_username: str = Depends(get_current_username)
):
    """获取当前用户信息"""
    try:
        user_data = UserResponse(username=current_username)
        return StandardResponse[UserResponse](
            code=200,
            message="success",
            data=user_data
        )
    except Exception as e:
        return StandardErrorResponse(
            code=1005,
            message="内部服务器错误",
            details=str(e)
        )


@router.post("/logout", summary="用户登出")
async def logout(
    current_username: str = Depends(get_current_username)
):
    """用户登出接口"""
    try:
        # 在实际应用中，这里可以将令牌加入黑名单
        # 目前只是返回成功响应，前端负责清除本地存储的令牌
        return StandardResponse[None](
            code=200,
            message="登出成功",
            data=None
        )
    except Exception as e:
        return StandardErrorResponse(
            code=1005,
            message="内部服务器错误",
            details=str(e)
        )