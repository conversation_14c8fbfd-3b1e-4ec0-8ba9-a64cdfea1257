"""Messages API routes."""

from typing import Dict, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from crud.database import get_database_session
from schemas.message import MessageResponse
from schemas.response import StandardResponse, StandardErrorResponse
from services.message_service import MessageService
from utils.dependencies import get_current_username

router = APIRouter(prefix="/api", tags=["消息管理"])
message_service = MessageService()


@router.get(
    "/messages", summary="获取历史消息"
)
async def get_messages(
    conversation_id: int = Query(..., description="对话ID"),
    current_username: str = Depends(get_current_username),
    db: Session = Depends(get_database_session),
):
    """获取指定对话的历史消息"""
    try:
        messages_data = message_service.get_messages_by_conversation_id(
            db, conversation_id, current_username
        )
        return StandardResponse[List[MessageResponse]](
            code=200,
            message="success",
            data=messages_data
        )
    except Exception as e:
        return StandardErrorResponse(
            code=1005,
            message="内部服务器错误",
            details=str(e)
        )