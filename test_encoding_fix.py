#!/usr/bin/env python3
"""
测试编码修复的脚本
验证中文字符在API请求中的处理
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8001"

def test_login():
    """测试登录获取token"""
    print("1. 测试登录...")
    
    login_data = {
        "username": "test_user",
        "password": "test_password"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/login", json=login_data)
        print(f"   登录响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                token = result['data']['access_token']
                print(f"   ✅ 登录成功，获取到token: {token[:20]}...")
                return token
            else:
                print(f"   ❌ 登录失败: {result.get('message')}")
        else:
            print(f"   ❌ 登录请求失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 登录异常: {e}")
    
    return None

def test_create_conversation_with_chinese(token):
    """测试创建包含中文的对话"""
    print("\n2. 测试创建包含中文的对话...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 测试不同的中文标题
    test_titles = [
        "对话 2025/7/29 13:44:28",  # 原始报错的标题
        "测试中文对话",
        "包含特殊字符的对话：！@#￥%……&*（）",
        "很长的中文标题" * 10,  # 测试长度限制
        "",  # 测试空标题
        None,  # 测试None值
    ]
    
    for i, title in enumerate(test_titles):
        print(f"\n   测试 {i+1}: 标题 = {repr(title)}")
        
        try:
            # 构造请求数据
            if title is None:
                data = {}  # 不包含title字段
            else:
                data = {"title": title}
            
            # 发送请求
            response = requests.post(
                f"{BASE_URL}/api/conversations/new", 
                json=data, 
                headers=headers
            )
            
            print(f"      响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 200:
                    conv_id = result['data']['id']
                    conv_title = result['data']['title']
                    print(f"      ✅ 创建成功: ID={conv_id}, 标题='{conv_title}'")
                else:
                    print(f"      ❌ 业务失败: {result.get('message')}")
            else:
                print(f"      ❌ 请求失败: {response.text}")
                
        except Exception as e:
            print(f"      ❌ 异常: {e}")

def test_encoding_edge_cases(token):
    """测试编码边界情况"""
    print("\n3. 测试编码边界情况...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json; charset=utf-8"
    }
    
    # 测试各种编码情况
    test_cases = [
        {
            "name": "UTF-8中文",
            "data": {"title": "测试UTF-8编码的中文标题"}
        },
        {
            "name": "包含emoji",
            "data": {"title": "测试emoji 😀🎉🚀"}
        },
        {
            "name": "混合字符",
            "data": {"title": "Mixed中文English123!@#"}
        }
    ]
    
    for case in test_cases:
        print(f"\n   测试: {case['name']}")
        
        try:
            # 手动构造JSON字符串确保UTF-8编码
            json_str = json.dumps(case['data'], ensure_ascii=False)
            json_bytes = json_str.encode('utf-8')
            
            response = requests.post(
                f"{BASE_URL}/api/conversations/new",
                data=json_bytes,
                headers=headers
            )
            
            print(f"      响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 200:
                    print(f"      ✅ 成功: {result['data']['title']}")
                else:
                    print(f"      ❌ 业务失败: {result.get('message')}")
            else:
                print(f"      ❌ 请求失败: {response.text}")
                
        except Exception as e:
            print(f"      ❌ 异常: {e}")

def main():
    """主测试函数"""
    print("=== 编码修复测试 ===")
    
    # 1. 登录获取token
    token = test_login()
    if not token:
        print("无法获取token，终止测试")
        sys.exit(1)
    
    # 2. 测试中文对话创建
    test_create_conversation_with_chinese(token)
    
    # 3. 测试编码边界情况
    test_encoding_edge_cases(token)
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
