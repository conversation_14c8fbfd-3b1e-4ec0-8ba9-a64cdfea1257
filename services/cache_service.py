"""缓存服务实现"""

import hashlib
import json
import logging
from datetime import datetime, timedelta
from functools import wraps
from typing import Optional, Any, Dict, List

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    redis = None

logger = logging.getLogger(__name__)


class CacheService:
    """缓存服务类"""

    def __init__(self):
        self.redis_client = None
        self._memory_cache = {}  # 内存缓存作为备选方案
        self._memory_cache_ttl = {}  # 内存缓存TTL

        if REDIS_AVAILABLE:
            try:
                # 延迟导入settings避免循环导入
                from config.settings import settings

                self.redis_client = redis.Redis(
                    host=settings.REDIS_HOST,
                    port=settings.REDIS_PORT,
                    password=settings.REDIS_PASSWORD,
                    db=settings.REDIS_DB,
                    decode_responses=True,
                    socket_timeout=5,
                    socket_connect_timeout=5,
                    retry_on_timeout=True,
                    health_check_interval=30
                )
                # 测试连接
                self.redis_client.ping()
                logger.info("Redis cache service initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to connect to Redis: {e}, falling back to memory cache")
                self.redis_client = None
        else:
            logger.warning("Redis not available, using memory cache")

    def _generate_key(self, prefix: str, *args, **kwargs) -> str:
        """生成缓存键"""
        key_data = f"{prefix}:{':'.join(map(str, args))}"
        if kwargs:
            key_data += f":{json.dumps(kwargs, sort_keys=True)}"
        return hashlib.md5(key_data.encode()).hexdigest()

    def _clean_memory_cache(self):
        """清理过期的内存缓存"""
        now = datetime.now()
        expired_keys = [
            key for key, ttl in self._memory_cache_ttl.items()
            if ttl < now
        ]
        for key in expired_keys:
            self._memory_cache.pop(key, None)
            self._memory_cache_ttl.pop(key, None)

    def set(self, key: str, value: Any, ttl: int = None) -> bool:
        """设置缓存"""
        if ttl is None:
            from config.settings import settings
            ttl = settings.CACHE_TTL

        try:
            if self.redis_client:
                # 使用Redis
                serialized_value = json.dumps(value, ensure_ascii=False)
                return self.redis_client.setex(key, ttl, serialized_value)
            else:
                # 使用内存缓存
                self._clean_memory_cache()
                self._memory_cache[key] = value
                self._memory_cache_ttl[key] = datetime.now() + timedelta(seconds=ttl)
                return True
        except Exception as e:
            logger.error(f"Failed to set cache for key {key}: {e}")
            return False

    def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        try:
            if self.redis_client:
                # 使用Redis
                value = self.redis_client.get(key)
                if value:
                    return json.loads(value)
                return None
            else:
                # 使用内存缓存
                self._clean_memory_cache()
                return self._memory_cache.get(key)
        except Exception as e:
            logger.error(f"Failed to get cache for key {key}: {e}")
            return None

    def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            if self.redis_client:
                return bool(self.redis_client.delete(key))
            else:
                self._memory_cache.pop(key, None)
                self._memory_cache_ttl.pop(key, None)
                return True
        except Exception as e:
            logger.error(f"Failed to delete cache for key {key}: {e}")
            return False

    def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            if self.redis_client:
                return bool(self.redis_client.exists(key))
            else:
                self._clean_memory_cache()
                return key in self._memory_cache
        except Exception as e:
            logger.error(f"Failed to check cache existence for key {key}: {e}")
            return False

    def clear_pattern(self, pattern: str) -> int:
        """清除匹配模式的缓存"""
        try:
            if self.redis_client:
                keys = self.redis_client.keys(pattern)
                if keys:
                    return self.redis_client.delete(*keys)
                return 0
            else:
                # 内存缓存简单匹配
                import fnmatch
                matched_keys = [
                    key for key in self._memory_cache.keys()
                    if fnmatch.fnmatch(key, pattern)
                ]
                for key in matched_keys:
                    self._memory_cache.pop(key, None)
                    self._memory_cache_ttl.pop(key, None)
                return len(matched_keys)
        except Exception as e:
            logger.error(f"Failed to clear cache pattern {pattern}: {e}")
            return 0

    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            if self.redis_client:
                info = self.redis_client.info()
                return {
                    "type": "redis",
                    "connected": True,
                    "used_memory": info.get("used_memory_human", "N/A"),
                    "connected_clients": info.get("connected_clients", 0),
                    "total_commands_processed": info.get("total_commands_processed", 0),
                }
            else:
                self._clean_memory_cache()
                return {
                    "type": "memory",
                    "connected": True,
                    "cache_size": len(self._memory_cache),
                    "memory_usage": f"{len(str(self._memory_cache))} bytes"
                }
        except Exception as e:
            logger.error(f"Failed to get cache stats: {e}")
            return {"type": "unknown", "connected": False, "error": str(e)}


# 全局缓存服务实例
cache_service = CacheService()


def cache_result(prefix: str, ttl: int = None):
    """缓存装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = cache_service._generate_key(prefix, *args, **kwargs)

            # 尝试从缓存获取
            cached_result = cache_service.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for key: {cache_key}")
                return cached_result

            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_service.set(cache_key, result, ttl)
            logger.debug(f"Cache set for key: {cache_key}")

            return result
        return wrapper
    return decorator


def cache_rag_result(query: str, collection_name: str, result: str, ttl: int = 3600) -> bool:
    """缓存RAG检索结果"""
    cache_key = cache_service._generate_key("rag", query, collection_name)
    return cache_service.set(cache_key, result, ttl)


def get_cached_rag_result(query: str, collection_name: str) -> Optional[str]:
    """获取缓存的RAG检索结果"""
    cache_key = cache_service._generate_key("rag", query, collection_name)
    return cache_service.get(cache_key)


def cache_user_conversations(username: str, conversations: List[Dict], ttl: int = 600) -> bool:
    """缓存用户会话列表"""
    cache_key = cache_service._generate_key("user_conversations", username)
    return cache_service.set(cache_key, conversations, ttl)


def get_cached_user_conversations(username: str) -> Optional[List[Dict]]:
    """获取缓存的用户会话列表"""
    cache_key = cache_service._generate_key("user_conversations", username)
    return cache_service.get(cache_key)


def invalidate_user_cache(username: str) -> int:
    """清除用户相关缓存"""
    pattern = f"*{username}*"
    return cache_service.clear_pattern(pattern)


async def check_cache_health() -> bool:
    """检查缓存服务健康状态"""
    try:
        test_key = "health_check"
        test_value = "ok"

        # 设置测试值
        if not cache_service.set(test_key, test_value, 10):
            return False

        # 获取测试值
        cached_value = cache_service.get(test_key)
        if cached_value != test_value:
            return False

        # 删除测试值
        cache_service.delete(test_key)
        return True
    except Exception as e:
        logger.error(f"Cache health check failed: {e}")
        return False
