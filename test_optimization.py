#!/usr/bin/env python3
"""
测试优化功能的脚本
"""

import requests
import json
import time

BASE_URL = "http://localhost:8001"

def test_login():
    """测试登录功能"""
    print("=== 测试登录功能 ===")
    
    login_data = {
        "username": "testuser",
        "password": "testpass"
    }
    
    response = requests.post(f"{BASE_URL}/api/login", json=login_data)
    print(f"登录响应状态: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"登录成功: {data['message']}")
        return data['data']['access_token']
    else:
        print(f"登录失败: {response.text}")
        return None

def test_conversations(token):
    """测试对话管理功能"""
    print("\n=== 测试对话管理功能 ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 创建新对话
    print("1. 创建新对话...")
    create_data = {"title": "测试对话"}
    response = requests.post(f"{BASE_URL}/api/conversations/new", json=create_data, headers=headers)
    print(f"创建对话响应: {response.status_code}")
    
    if response.status_code == 200:
        conversation_data = response.json()['data']
        conversation_id = conversation_data['id']
        print(f"对话创建成功，ID: {conversation_id}")
        
        # 获取对话列表
        print("2. 获取对话列表...")
        response = requests.get(f"{BASE_URL}/api/conversations", headers=headers)
        print(f"获取对话列表响应: {response.status_code}")
        
        if response.status_code == 200:
            conversations = response.json()['data']
            print(f"对话列表长度: {len(conversations)}")
            for conv in conversations:
                print(f"  - ID: {conv['id']}, 标题: {conv['title']}, 置顶: {conv.get('sticky_flag', False)}")
        
        # 测试置顶功能
        print("3. 测试置顶功能...")
        sticky_data = {"sticky_flag": True}
        response = requests.put(f"{BASE_URL}/api/conversations/{conversation_id}/sticky", 
                              json=sticky_data, headers=headers)
        print(f"置顶对话响应: {response.status_code}")
        
        if response.status_code == 200:
            print("置顶成功")
            
            # 再次获取对话列表验证置顶
            response = requests.get(f"{BASE_URL}/api/conversations", headers=headers)
            if response.status_code == 200:
                conversations = response.json()['data']
                for conv in conversations:
                    if conv['id'] == conversation_id:
                        print(f"对话置顶状态: {conv.get('sticky_flag', False)}")
        
        return conversation_id
    
    return None

def test_messages(token, conversation_id):
    """测试消息管理功能"""
    print("\n=== 测试消息管理功能 ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 发送消息
    print("1. 发送消息...")
    chat_data = {
        "conversation_id": conversation_id,
        "message": "这是一条测试消息",
        "collection_name": "FinancialResearchOffice",
        "input": {},
        "parent_msg_id": 0
    }
    
    response = requests.post(f"{BASE_URL}/api/chat/stream", json=chat_data, headers=headers)
    print(f"发送消息响应: {response.status_code}")
    
    if response.status_code == 200:
        print("消息发送成功")
        
        # 等待一下让消息保存
        time.sleep(2)
        
        # 获取消息列表
        print("2. 获取消息列表...")
        response = requests.get(f"{BASE_URL}/api/messages?conversation_id={conversation_id}", headers=headers)
        print(f"获取消息响应: {response.status_code}")
        
        if response.status_code == 200:
            messages_data = response.json()['data']
            messages = messages_data.get(str(conversation_id), [])
            print(f"消息数量: {len(messages)}")
            
            for msg in messages:
                print(f"  - 角色: {msg['role']}, 内容: {msg['content'][:50]}..., 父消息ID: {msg.get('parent_msg_id', 0)}")

def test_logout(token):
    """测试登出功能"""
    print("\n=== 测试登出功能 ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.post(f"{BASE_URL}/api/logout", headers=headers)
    print(f"登出响应: {response.status_code}")
    
    if response.status_code == 200:
        print("登出成功")
    else:
        print(f"登出失败: {response.text}")

def main():
    """主测试函数"""
    print("开始测试优化功能...")
    
    # 测试登录
    token = test_login()
    if not token:
        print("登录失败，无法继续测试")
        return
    
    # 测试对话管理
    conversation_id = test_conversations(token)
    if not conversation_id:
        print("对话创建失败，无法测试消息功能")
        return
    
    # 测试消息管理
    test_messages(token, conversation_id)
    
    # 测试登出
    test_logout(token)
    
    print("\n所有测试完成！")

if __name__ == "__main__":
    main()
