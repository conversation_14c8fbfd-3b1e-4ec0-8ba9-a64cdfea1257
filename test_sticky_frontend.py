#!/usr/bin/env python3
"""
测试前端置顶功能的脚本
"""

import requests
import json
import time

BASE_URL = "http://localhost:8001"

def test_sticky_functionality():
    """测试置顶功能"""
    print("=== 测试置顶功能 ===")
    
    # 1. 登录
    login_data = {
        "username": "testuser",
        "password": "testpass"
    }
    
    response = requests.post(f"{BASE_URL}/api/login", json=login_data)
    if response.status_code != 200:
        print(f"登录失败: {response.status_code}")
        return
    
    token = response.json()['data']['access_token']
    headers = {"Authorization": f"Bearer {token}"}
    
    # 2. 创建一个新对话
    create_data = {"title": "置顶测试对话"}
    response = requests.post(f"{BASE_URL}/api/conversations/new", json=create_data, headers=headers)
    if response.status_code != 200:
        print(f"创建对话失败: {response.status_code}")
        return
    
    conversation_id = response.json()['data']['id']
    print(f"创建对话成功，ID: {conversation_id}")
    
    # 3. 获取对话列表，检查初始状态
    response = requests.get(f"{BASE_URL}/api/conversations", headers=headers)
    if response.status_code != 200:
        print(f"获取对话列表失败: {response.status_code}")
        return
    
    conversations = response.json()['data']
    target_conv = next((c for c in conversations if c['id'] == conversation_id), None)
    if target_conv:
        print(f"对话初始状态: sticky_flag={target_conv.get('sticky_flag', False)}")
    
    # 4. 设置置顶
    sticky_data = {"sticky_flag": True}
    response = requests.put(f"{BASE_URL}/api/conversations/{conversation_id}/sticky", 
                          json=sticky_data, headers=headers)
    if response.status_code != 200:
        print(f"设置置顶失败: {response.status_code} - {response.text}")
        return
    
    print("设置置顶成功")
    print(f"API返回: {response.json()}")
    
    # 5. 再次获取对话列表，验证状态
    response = requests.get(f"{BASE_URL}/api/conversations", headers=headers)
    if response.status_code == 200:
        conversations = response.json()['data']
        target_conv = next((c for c in conversations if c['id'] == conversation_id), None)
        if target_conv:
            print(f"对话更新后状态: sticky_flag={target_conv.get('sticky_flag', False)}")
            print(f"完整对话信息: {json.dumps(target_conv, indent=2, ensure_ascii=False)}")
        else:
            print("未找到目标对话")
    
    # 6. 取消置顶
    sticky_data = {"sticky_flag": False}
    response = requests.put(f"{BASE_URL}/api/conversations/{conversation_id}/sticky", 
                          json=sticky_data, headers=headers)
    if response.status_code == 200:
        print("取消置顶成功")
        
        # 验证取消置顶后的状态
        response = requests.get(f"{BASE_URL}/api/conversations", headers=headers)
        if response.status_code == 200:
            conversations = response.json()['data']
            target_conv = next((c for c in conversations if c['id'] == conversation_id), None)
            if target_conv:
                print(f"取消置顶后状态: sticky_flag={target_conv.get('sticky_flag', False)}")
    else:
        print(f"取消置顶失败: {response.status_code} - {response.text}")

if __name__ == "__main__":
    test_sticky_functionality()
