#!/usr/bin/env python3
"""
测试text/plain Content-Type的脚本
模拟前端发送错误的Content-Type
"""

import requests
import json

BASE_URL = "http://localhost:8001"

def test_login():
    """测试登录获取token"""
    print("1. 测试登录...")
    
    login_data = {
        "username": "test_user",
        "password": "test_password"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/login", json=login_data)
        print(f"   登录响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                token = result['data']['access_token']
                print(f"   ✅ 登录成功")
                return token
            else:
                print(f"   ❌ 登录失败: {result.get('message')}")
        else:
            print(f"   ❌ 登录请求失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 登录异常: {e}")
    
    return None

def test_text_plain_content_type(token):
    """测试使用text/plain Content-Type发送JSON数据"""
    print("\n2. 测试 text/plain Content-Type...")
    
    test_data = {"title": "测试对话 2025/7/29"}
    json_str = json.dumps(test_data, ensure_ascii=False)
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "text/plain; charset=UTF-8"  # 错误的Content-Type
    }
    
    print(f"   发送数据: {json_str}")
    print(f"   Content-Type: {headers['Content-Type']}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/conversations/new",
            data=json_str.encode('utf-8'),
            headers=headers
        )
        
        print(f"   响应状态码: {response.status_code}")
        print(f"   响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                print(f"   ✅ 意外成功: {result['data']['title']}")
            else:
                print(f"   ❌ 业务失败: {result.get('message')}")
        else:
            print(f"   ❌ 请求失败 (预期的)")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")

def test_correct_content_type(token):
    """测试使用正确的Content-Type"""
    print("\n3. 测试正确的 Content-Type...")
    
    test_data = {"title": "测试对话 2025/7/29"}
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json; charset=utf-8"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/conversations/new",
            json=test_data,
            headers=headers
        )
        
        print(f"   响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                print(f"   ✅ 成功: {result['data']['title']}")
            else:
                print(f"   ❌ 业务失败: {result.get('message')}")
        else:
            print(f"   ❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")

def main():
    """主测试函数"""
    print("=== text/plain Content-Type 测试 ===")
    
    # 1. 登录获取token
    token = test_login()
    if not token:
        print("无法获取token，终止测试")
        return
    
    # 2. 测试错误的Content-Type
    test_text_plain_content_type(token)
    
    # 3. 测试正确的Content-Type
    test_correct_content_type(token)
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
