#!/usr/bin/env python3
"""
测试引用功能的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.llm_service import get_llm_service
from schemas.message import ChatRequest
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_references():
    """测试引用功能"""
    try:
        # 创建LLM服务实例
        llm_service = get_llm_service()
        
        # 创建测试请求
        test_message = "什么是人工智能？"
        collection_name = "FinancialResearchOffice"
        
        logger.info(f"测试消息: {test_message}")
        logger.info(f"集合名称: {collection_name}")
        
        # 测试新的方法
        content_generator, references = llm_service.generate_rag_response_with_references(
            test_message, collection_name, {}
        )
        
        logger.info(f"获取到 {len(references)} 个引用")
        
        # 打印引用信息
        for i, ref in enumerate(references, 1):
            logger.info(f"引用 {i}:")
            logger.info(f"  标题: {ref.get('title', 'N/A')}")
            logger.info(f"  得分: {ref.get('score', 0.0):.3f}")
            logger.info(f"  内容长度: {len(ref.get('content', ''))}")
            logger.info(f"  来源: {ref.get('source', 'N/A')}")
        
        # 收集生成的内容
        content = ""
        logger.info("开始收集生成的内容...")
        for chunk in content_generator:
            content += chunk
            if len(content) > 100:  # 只收集前100个字符用于测试
                break
        
        logger.info(f"生成的内容开头: {content[:100]}...")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    logger.info("开始测试引用功能...")
    success = test_references()
    if success:
        logger.info("测试成功完成!")
    else:
        logger.error("测试失败!")
        sys.exit(1)
