#!/usr/bin/env python3
"""
测试SSE流中的引用内容
"""

import requests
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_sse_references():
    """测试SSE流中的引用内容"""
    try:
        # 首先登录获取token
        login_response = requests.post('http://localhost:8001/api/login', json={
            'username': 'test_user',
            'password': 'test_password'
        })
        
        if login_response.status_code != 200:
            logger.error(f"登录失败: {login_response.status_code}")
            return False
        
        login_data = login_response.json()
        logger.info(f"登录响应: {login_data}")

        # 检查不同的响应格式
        if login_data.get('success'):
            token = login_data['data']['access_token']
        elif login_data.get('code') == 200:
            token = login_data['data']['access_token']
        else:
            logger.error(f"登录失败: {login_data}")
            return False
        logger.info("登录成功，获取到token")
        
        # 创建对话
        conv_response = requests.post('http://localhost:8001/api/conversations/new', 
            json={'title': '测试引用功能'},
            headers={'Authorization': f'Bearer {token}'}
        )
        
        if conv_response.status_code != 200:
            logger.error(f"创建对话失败: {conv_response.status_code}")
            return False
        
        conv_data = conv_response.json()
        logger.info(f"创建对话响应: {conv_data}")

        # 检查不同的响应格式
        if conv_data.get('success'):
            conversation_id = conv_data['data']['id']
        elif conv_data.get('code') == 200:
            conversation_id = conv_data['data']['id']
        else:
            logger.error(f"创建对话失败: {conv_data}")
            return False
        logger.info(f"创建对话成功: {conversation_id}")
        
        # 发送流式消息
        stream_response = requests.post('http://localhost:8001/api/chat/stream',
            json={
                'conversation_id': conversation_id,
                'message': '什么是金融？',
                'collection_name': 'FinancialResearchOffice',
                'input': {}
            },
            headers={'Authorization': f'Bearer {token}'},
            stream=True
        )
        
        if stream_response.status_code != 200:
            logger.error(f"流式请求失败: {stream_response.status_code}")
            return False
        
        logger.info("开始接收流式响应...")
        
        references_received = False
        content_chunks = []
        
        # 解析SSE流
        for line in stream_response.iter_lines(decode_unicode=True):
            if line.startswith('data: '):
                data_str = line[6:].strip()
                
                if data_str == '[DONE]':
                    logger.info("流式响应完成")
                    break
                
                try:
                    data = json.loads(data_str)
                    
                    if data.get('code') == 200 and data.get('data'):
                        data_content = data['data']
                        
                        if data_content.get('type') == 'references':
                            references = data_content.get('references', [])
                            logger.info(f"✅ 接收到引用内容: {len(references)} 个引用")
                            for i, ref in enumerate(references, 1):
                                logger.info(f"  引用 {i}: {ref.get('title', 'N/A')} (得分: {ref.get('score', 0.0):.3f})")
                            references_received = True
                            
                        elif data_content.get('type') == 'content':
                            content = data_content.get('content', '')
                            content_chunks.append(content)
                            
                        elif data_content.get('content'):  # 向后兼容
                            content = data_content.get('content', '')
                            content_chunks.append(content)
                
                except json.JSONDecodeError as e:
                    logger.error(f"解析JSON失败: {e}, 数据: {data_str}")
        
        full_content = ''.join(content_chunks)
        logger.info(f"✅ 接收到完整内容: {len(full_content)} 字符")
        logger.info(f"内容开头: {full_content[:100]}...")
        
        if references_received:
            logger.info("✅ 引用内容通过SSE流成功返回!")
            return True
        else:
            logger.error("❌ 未接收到引用内容")
            return False
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    logger.info("开始测试SSE流中的引用内容...")
    success = test_sse_references()
    if success:
        logger.info("✅ 测试成功完成!")
    else:
        logger.error("❌ 测试失败!")
        exit(1)
