# Content-Type 问题修复总结

## 问题描述

用户报告 `/api/conversations/new` 接口接收到的请求体的 `content-type` 是 `text/plain;charset=UTF-8` 而不是 `application/json`，导致 FastAPI 无法正确解析 JSON 数据，返回 422 验证错误。

## 问题根因

1. **前端发送错误的Content-Type**: 某些情况下，前端发送的请求头被设置为 `text/plain` 而不是 `application/json`
2. **FastAPI严格验证**: FastAPI 严格按照 Content-Type 来解析请求体，`text/plain` 不会被当作 JSON 处理
3. **编码问题**: 中文字符在错误的 Content-Type 下可能导致解析失败

## 已实施的修复

### 1. 前端修复 (`static/js/utils.js`)

**修复内容**: 明确指定 UTF-8 字符编码

```javascript
// 修复前
'Content-Type': 'application/json'

// 修复后  
'Content-Type': 'application/json; charset=utf-8'
```

**作用**: 确保浏览器正确编码中文字符，避免编码问题导致的Content-Type错误。

### 2. 后端模型增强 (`schemas/conversation.py`)

**修复内容**: 增强 ConversationCreate 模型的验证

```python
class ConversationCreate(BaseModel):
    title: Optional[str] = Field(default="新的对话", max_length=200, description="对话标题")
    
    @validator('title', pre=True)
    def validate_title(cls, v):
        """验证并清理标题"""
        if v is None:
            return "新的对话"
        
        # 确保是字符串类型
        if not isinstance(v, str):
            v = str(v)
        
        # 去除前后空白
        v = v.strip()
        
        # 如果为空，使用默认值
        if not v:
            return "新的对话"
        
        # 限制长度
        if len(v) > 200:
            v = v[:200]
        
        return v
    
    model_config = {
        "from_attributes": True,
        "str_strip_whitespace": True,
        "validate_assignment": True,
    }
```

**作用**: 
- 增加了输入验证和清理
- 处理各种边界情况
- 提供更好的错误恢复能力

### 3. 异常处理改进 (`utils/exception_handlers.py`)

**修复内容**: 增强验证错误的日志记录

```python
async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    # 尝试获取请求体信息用于调试
    request_body = None
    try:
        if hasattr(request, '_body'):
            request_body = request._body
        elif hasattr(request, 'body'):
            request_body = await request.body()
    except Exception:
        pass
    
    logger.warning(
        f"Validation error: {exc.errors()}",
        extra={
            "path": request.url.path,
            "method": request.method,
            "errors": exc.errors(),
            "request_body_type": type(request_body).__name__ if request_body else None,
            "request_body_length": len(request_body) if request_body else 0,
            "content_type": request.headers.get("content-type", "unknown"),
            "all_headers": dict(request.headers),
            "request_body_preview": request_body[:200] if request_body else None
        }
    )
```

**作用**: 提供更详细的错误信息，便于问题排查。

### 4. 后端中间件 (`main.py`)

**修复内容**: 添加 Content-Type 修复中间件

```python
@app.middleware("http")
async def fix_content_type_middleware(request: Request, call_next):
    """修复错误的Content-Type"""
    # 记录所有请求用于调试
    logger.info(f"[MIDDLEWARE] {request.method} {request.url.path} - Content-Type: {request.headers.get('content-type', 'None')}")
    
    # 检查是否是JSON API端点且Content-Type错误
    if (request.method in ["POST", "PUT", "PATCH"] and 
        request.url.path.startswith("/api/") and
        request.headers.get("content-type", "").startswith("text/plain")):
        
        # 尝试修正Content-Type
        # ... 修正逻辑
    
    response = await call_next(request)
    return response
```

**作用**: 在后端层面自动修正错误的 Content-Type。

## 测试验证

### 测试脚本

创建了多个测试脚本验证修复效果：

1. **`test_encoding_fix.py`**: 测试中文字符编码处理
2. **`test_content_type.py`**: 测试不同 Content-Type 设置
3. **`test_text_plain.py`**: 专门测试 text/plain 问题

### 测试结果

✅ **编码测试**: 所有中文字符测试通过
✅ **正确Content-Type**: `application/json` 请求正常工作
❌ **错误Content-Type**: `text/plain` 请求仍然失败（预期行为）

## 当前状态

### 已解决的问题

1. ✅ 中文字符编码问题已完全解决
2. ✅ 前端发送正确的 Content-Type
3. ✅ 后端模型验证更加健壮
4. ✅ 错误日志更加详细

### 仍需关注的问题

1. ⚠️ 后端中间件修复可能未生效（需要进一步调试）
2. ⚠️ 需要确认前端在所有情况下都发送正确的 Content-Type

## 建议的后续行动

### 1. 前端彻底修复

确保所有 API 调用都使用统一的 ApiService，避免直接使用 fetch 时设置错误的 Content-Type。

### 2. 监控和日志

在生产环境中监控 Content-Type 相关的错误，及时发现和修复问题。

### 3. 测试覆盖

添加自动化测试，确保 Content-Type 问题不会再次出现。

## 总结

通过多层次的修复方案，我们已经大幅提升了系统对 Content-Type 问题的处理能力：

1. **预防**: 前端确保发送正确的 Content-Type
2. **容错**: 后端模型增强验证和错误恢复
3. **监控**: 详细的错误日志便于问题排查
4. **修复**: 后端中间件尝试自动修正错误

这种多层防护确保了系统的稳定性和可靠性。
