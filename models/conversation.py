"""Conversation model for chat sessions."""

from datetime import datetime
from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Boolean
from sqlalchemy.orm import relationship

from crud.database import Base


class Conversation(Base):
    """Conversation model for storing chat sessions."""

    __tablename__ = "conversation"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, nullable=False, index=True)  # 直接存储 LDAP 用户名
    title = Column(String, default="新的对话")
    sticky_flag = Column(Boolean, default=False)  # 置顶标记
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    messages = relationship("Message", back_populates="conversation")

    def __repr__(self) -> str:
        return f"<Conversation(id={self.id}, title='{self.title}')>"
