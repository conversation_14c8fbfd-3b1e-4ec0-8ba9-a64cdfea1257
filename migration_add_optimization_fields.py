#!/usr/bin/env python3
"""
数据库迁移脚本：添加优化字段
为现有的conversation和message表添加新字段以支持置顶功能和父子消息关系
"""

import sqlite3
import logging
from datetime import datetime
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_database(db_path: str = "database.db"):
    """执行数据库迁移"""
    
    if not Path(db_path).exists():
        logger.error(f"数据库文件 {db_path} 不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查是否已经迁移过
        cursor.execute("PRAGMA table_info(conversation)")
        conversation_columns = [column[1] for column in cursor.fetchall()]
        
        cursor.execute("PRAGMA table_info(message)")
        message_columns = [column[1] for column in cursor.fetchall()]
        
        # 迁移conversation表
        if 'sticky_flag' not in conversation_columns:
            logger.info("添加conversation表的sticky_flag字段...")
            cursor.execute("ALTER TABLE conversation ADD COLUMN sticky_flag BOOLEAN DEFAULT FALSE")
        
        if 'updated_at' not in conversation_columns:
            logger.info("添加conversation表的updated_at字段...")
            cursor.execute("ALTER TABLE conversation ADD COLUMN updated_at TIMESTAMP")
            # 将updated_at设置为与created_at相同的值
            cursor.execute("UPDATE conversation SET updated_at = created_at WHERE updated_at IS NULL")
        
        # 迁移message表
        if 'parent_msg_id' not in message_columns:
            logger.info("添加message表的parent_msg_id字段...")
            cursor.execute("ALTER TABLE message ADD COLUMN parent_msg_id INTEGER DEFAULT 0")
        
        if 'created_at' not in message_columns:
            logger.info("添加message表的created_at字段...")
            cursor.execute("ALTER TABLE message ADD COLUMN created_at TIMESTAMP")
            # 将created_at设置为与timestamp相同的值
            cursor.execute("UPDATE message SET created_at = timestamp WHERE created_at IS NULL")

        if 'updated_at' not in message_columns:
            logger.info("添加message表的updated_at字段...")
            cursor.execute("ALTER TABLE message ADD COLUMN updated_at TIMESTAMP")
            # 将updated_at设置为与timestamp相同的值
            cursor.execute("UPDATE message SET updated_at = timestamp WHERE updated_at IS NULL")
        
        # 提交更改
        conn.commit()
        logger.info("数据库迁移完成")
        
        # 验证迁移结果
        cursor.execute("PRAGMA table_info(conversation)")
        conversation_columns_after = [column[1] for column in cursor.fetchall()]
        
        cursor.execute("PRAGMA table_info(message)")
        message_columns_after = [column[1] for column in cursor.fetchall()]
        
        logger.info(f"Conversation表字段: {conversation_columns_after}")
        logger.info(f"Message表字段: {message_columns_after}")
        
        return True
        
    except Exception as e:
        logger.error(f"数据库迁移失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def main():
    """主函数"""
    logger.info("开始数据库迁移...")
    success = migrate_database()
    
    if success:
        logger.info("数据库迁移成功完成！")
    else:
        logger.error("数据库迁移失败！")
        exit(1)

if __name__ == "__main__":
    main()
