#!/usr/bin/env python3
"""
测试优化功能的脚本
验证新添加的置顶功能、父子消息关系等功能是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from crud.database import SessionLocal, create_tables
from crud.conversation_dao import ConversationDao
from crud.message_dao import MessageDao
from services.conversation_service import ConversationService
from services.message_service import MessageService
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_conversation_sticky_functionality():
    """测试对话置顶功能"""
    logger.info("测试对话置顶功能...")
    
    db: Session = SessionLocal()
    try:
        # 创建测试用户和对话
        username = "test_user"
        conversation_service = ConversationService()
        
        # 创建两个对话
        conv1 = conversation_service.create_conversation(db, username, "对话1")
        conv2 = conversation_service.create_conversation(db, username, "对话2")
        
        logger.info(f"创建对话1: {conv1.id}, sticky_flag: {conv1.sticky_flag}")
        logger.info(f"创建对话2: {conv2.id}, sticky_flag: {conv2.sticky_flag}")
        
        # 测试置顶功能
        updated_conv = conversation_service.update_sticky_status(db, conv1.id, username, True)
        logger.info(f"置顶对话1后: sticky_flag: {updated_conv.sticky_flag}")
        
        # 获取对话列表，验证置顶对话在前面
        conversations = conversation_service.get_user_conversations(db, username)
        logger.info("对话列表顺序:")
        for conv in conversations:
            logger.info(f"  对话{conv.id}: sticky={conv.sticky_flag}, title={conv.title}")
        
        # 验证置顶对话在前面
        assert conversations[0].sticky_flag == True, "置顶对话应该在第一位"
        logger.info("✓ 对话置顶功能测试通过")
        
    except Exception as e:
        logger.error(f"对话置顶功能测试失败: {e}")
        raise
    finally:
        db.close()

def test_message_parent_child_relationship():
    """测试消息父子关系功能"""
    logger.info("测试消息父子关系功能...")
    
    db: Session = SessionLocal()
    try:
        # 创建测试对话
        username = "test_user"
        conversation_service = ConversationService()
        message_service = MessageService()
        
        conv = conversation_service.create_conversation(db, username, "测试对话")
        
        # 创建第一条消息（父消息）
        parent_msg = message_service.create_user_message(db, conv.id, "这是第一条消息", username, 0)
        logger.info(f"创建父消息: {parent_msg.id}, parent_msg_id: {parent_msg.parent_msg_id}")
        
        # 创建回复消息（子消息）
        child_msg = message_service.create_assistant_message(db, conv.id, "这是回复消息", parent_msg.id)
        logger.info(f"创建子消息: {child_msg.id}, parent_msg_id: {child_msg.parent_msg_id}")
        
        # 验证父子关系
        assert parent_msg.parent_msg_id == 0, "第一条消息的parent_msg_id应该为0"
        assert child_msg.parent_msg_id == parent_msg.id, "子消息的parent_msg_id应该指向父消息"
        
        # 验证新字段存在
        assert hasattr(parent_msg, 'created_at'), "消息应该有created_at字段"
        assert hasattr(parent_msg, 'updated_at'), "消息应该有updated_at字段"
        
        logger.info("✓ 消息父子关系功能测试通过")
        
    except Exception as e:
        logger.error(f"消息父子关系功能测试失败: {e}")
        raise
    finally:
        db.close()

def test_conversation_time_fields():
    """测试对话时间字段功能"""
    logger.info("测试对话时间字段功能...")
    
    db: Session = SessionLocal()
    try:
        username = "test_user"
        conversation_service = ConversationService()
        
        # 创建对话
        conv = conversation_service.create_conversation(db, username, "时间测试对话")
        
        # 验证时间字段
        assert hasattr(conv, 'created_at'), "对话应该有created_at字段"
        assert hasattr(conv, 'updated_at'), "对话应该有updated_at字段"
        assert conv.created_at is not None, "created_at不应该为空"
        assert conv.updated_at is not None, "updated_at不应该为空"
        
        logger.info(f"对话创建时间: {conv.created_at}")
        logger.info(f"对话更新时间: {conv.updated_at}")
        
        # 更新置顶状态，验证updated_at是否更新
        import time
        time.sleep(1)  # 确保时间差异
        updated_conv = conversation_service.update_sticky_status(db, conv.id, username, True)
        
        assert updated_conv.updated_at > conv.created_at, "更新后的updated_at应该大于created_at"
        logger.info(f"更新后的时间: {updated_conv.updated_at}")
        
        logger.info("✓ 对话时间字段功能测试通过")
        
    except Exception as e:
        logger.error(f"对话时间字段功能测试失败: {e}")
        raise
    finally:
        db.close()

def main():
    """主测试函数"""
    logger.info("开始优化功能测试...")
    
    try:
        # 确保数据库表存在
        create_tables()
        
        # 运行各项测试
        test_conversation_sticky_functionality()
        test_message_parent_child_relationship()
        test_conversation_time_fields()
        
        logger.info("🎉 所有优化功能测试通过！")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
