<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端流式响应测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .message {
            background-color: #f5f5f5;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .user-message {
            background-color: #007bff;
            color: white;
            margin-left: 100px;
        }
        .ai-message {
            background-color: #f8f9fa;
            margin-right: 100px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .loading {
            color: #007bff;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div id="app">
        <h1>前端流式响应测试</h1>
        
        <div class="container">
            <h3>配置</h3>
            <div>
                <label>Token: </label>
                <input v-model="token" type="text" placeholder="输入认证token">
            </div>
            <div>
                <label>对话ID: </label>
                <input v-model="conversationId" type="number" placeholder="对话ID">
            </div>
            <div>
                <label>消息: </label>
                <textarea v-model="message" rows="3" placeholder="输入要发送的消息"></textarea>
            </div>
            <button @click="sendMessage" :disabled="isSending">
                {{ isSending ? '发送中...' : '发送消息' }}
            </button>
            <button @click="clearMessages">清空消息</button>
        </div>
        
        <div class="container">
            <h3>对话历史</h3>
            <div v-if="messages.length === 0" style="text-align: center; color: #666;">
                暂无消息
            </div>
            <div v-for="(msg, index) in messages" :key="index" 
                 :class="['message', msg.role === 'user' ? 'user-message' : 'ai-message']">
                <strong>{{ msg.role === 'user' ? '用户' : 'AI助手' }}:</strong><br>
                {{ msg.content }}
                <div style="font-size: 12px; opacity: 0.7; margin-top: 5px;">
                    {{ formatTime(msg.timestamp) }}
                </div>
            </div>
            <div v-if="isReceiving" class="message ai-message loading">
                <strong>AI助手:</strong><br>
                正在思考中...
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref } = Vue;

        createApp({
            setup() {
                const token = ref('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ3YW5nemhpeGluIiwidHlwZSI6ImFjY2Vzc190b2tlbiIsImlhdCI6MTc1Mzc1Njg4MSwiZXhwIjoxNzUzNzYwNDgxfQ.sMJJkGuXkfOnkkFCKf1r3o9SJjmA8a99-UARkwMNU00');
                const conversationId = ref(1753608474944);
                const message = ref('你好，请简单介绍一下自己');
                const messages = ref([]);
                const isSending = ref(false);
                const isReceiving = ref(false);

                const formatTime = (timestamp) => {
                    const date = new Date(timestamp);
                    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                };

                const clearMessages = () => {
                    messages.value = [];
                };

                const sendMessage = async () => {
                    if (!message.value.trim() || isSending.value) return;
                    
                    const userMessage = message.value.trim();
                    message.value = '';
                    
                    // 添加用户消息
                    messages.value.push({
                        role: 'user',
                        content: userMessage,
                        timestamp: new Date().toISOString()
                    });
                    
                    isSending.value = true;
                    isReceiving.value = true;
                    
                    try {
                        // 添加AI消息占位符
                        const aiMessageIndex = messages.value.length;
                        messages.value.push({
                            role: 'assistant',
                            content: '',
                            timestamp: new Date().toISOString()
                        });
                        
                        const response = await fetch('/api/chat/stream', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token.value}`
                            },
                            body: JSON.stringify({
                                conversation_id: conversationId.value,
                                message: userMessage,
                                collection_name: 'FinancialResearchOffice',
                                input: {}
                            })
                        });
                        
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        
                        const reader = response.body.getReader();
                        const decoder = new TextDecoder('utf-8');
                        let assistantResponse = '';
                        let buffer = '';
                        
                        while (true) {
                            const { done, value } = await reader.read();
                            
                            if (done) {
                                break;
                            }
                            
                            const chunk = decoder.decode(value, { stream: true });
                            buffer += chunk;
                            
                            const lines = buffer.split('\n');
                            buffer = lines.pop() || '';
                            
                            for (const line of lines) {
                                const trimmedLine = line.trim();
                                if (trimmedLine.startsWith('data: ')) {
                                    const data = trimmedLine.substring(6).trim();
                                    
                                    if (data === '[DONE]') {
                                        isReceiving.value = false;
                                        break;
                                    }
                                    
                                    try {
                                        const parsedData = JSON.parse(data);
                                        console.log('Received data:', parsedData);
                                        
                                        if (parsedData.code === 200 && parsedData.data?.content) {
                                            assistantResponse += parsedData.data.content;
                                            messages.value[aiMessageIndex].content = assistantResponse;
                                        } else if (parsedData.code !== 200 && parsedData.data?.error) {
                                            messages.value[aiMessageIndex].content = `错误: ${parsedData.data.error}`;
                                            isReceiving.value = false;
                                            break;
                                        }
                                    } catch (e) {
                                        console.error('解析数据失败:', e, 'Raw data:', data);
                                    }
                                }
                            }
                            
                            if (!isReceiving.value) {
                                break;
                            }
                        }
                        
                        // 处理缓冲区剩余数据
                        if (buffer.trim()) {
                            const trimmedLine = buffer.trim();
                            if (trimmedLine.startsWith('data: ')) {
                                const data = trimmedLine.substring(6).trim();
                                if (data !== '[DONE]') {
                                    try {
                                        const parsedData = JSON.parse(data);
                                        if (parsedData.code === 200 && parsedData.data?.content) {
                                            assistantResponse += parsedData.data.content;
                                            messages.value[aiMessageIndex].content = assistantResponse;
                                        }
                                    } catch (e) {
                                        console.error('解析缓冲区数据失败:', e, 'Raw data:', data);
                                    }
                                }
                            }
                        }
                        
                    } catch (error) {
                        console.error('Send message error:', error);
                        messages.value.push({
                            role: 'assistant',
                            content: `错误: ${error.message}`,
                            timestamp: new Date().toISOString()
                        });
                        isReceiving.value = false;
                    } finally {
                        isSending.value = false;
                    }
                };

                return {
                    token,
                    conversationId,
                    message,
                    messages,
                    isSending,
                    isReceiving,
                    formatTime,
                    clearMessages,
                    sendMessage
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
