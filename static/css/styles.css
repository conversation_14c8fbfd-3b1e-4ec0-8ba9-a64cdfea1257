.chat-container {
    height: calc(100vh - 100px);
}

.messages-container {
    height: calc(100vh - 220px);
}

.fade-enter-active, .fade-leave-active {
    transition: opacity 0.5s;
}

.fade-enter, .fade-leave-to {
    opacity: 0;
}

.slide-fade-enter-active {
    transition: all 0.3s ease;
}

.slide-fade-leave-active {
    transition: all 0.3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter-from, .slide-fade-leave-to {
    transform: translateX(20px);
    opacity: 0;
}

/* 添加滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* 引用内容样式 */
.references-section {
    border-top: 1px solid #e5e7eb;
    margin-top: 12px;
    padding-top: 12px;
}

.reference-item {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.reference-item:hover {
    border-color: #d1d5db;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 消息容器样式优化 */
.message-container {
    max-width: 75%;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.message-container.expanded {
    max-width: 85%;
}

.message-container.user-message {
    max-width: 75%;
    width: fit-content;
}

.reference-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 8px;
}

.reference-title {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
}

.reference-score {
    font-size: 0.75rem;
    color: #6b7280;
}

.reference-content {
    color: #4b5563;
    font-size: 0.75rem;
    line-height: 1.5;
    margin-bottom: 8px;
}

.reference-source {
    font-size: 0.75rem;
}

.reference-source a {
    color: #3b82f6;
    text-decoration: none;
    transition: color 0.2s ease;
}

.reference-source a:hover {
    color: #1d4ed8;
}

/* Markdown内容样式 */
.prose {
    max-width: none;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.prose.prose-xs {
    font-size: 0.75rem;
    line-height: 1.5;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
    word-wrap: break-word;
}

.prose p {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
    word-wrap: break-word;
}

.prose ul, .prose ol {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
}

.prose li {
    word-wrap: break-word;
}

.prose code {
    background-color: #f3f4f6;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.prose pre {
    background-color: #f3f4f6;
    padding: 0.75rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 0.5em 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.prose blockquote {
    border-left: 4px solid #e5e7eb;
    padding-left: 1rem;
    margin: 0.5em 0;
    color: #6b7280;
    word-wrap: break-word;
}

/* 引用内容中的特殊样式 */
.prose.prose-xs h1, .prose.prose-xs h2, .prose.prose-xs h3 {
    font-size: 0.875rem;
    font-weight: 600;
    margin-top: 0.25em;
    margin-bottom: 0.25em;
}

.prose.prose-xs p {
    margin-top: 0.25em;
    margin-bottom: 0.25em;
}

.prose.prose-xs ul, .prose.prose-xs ol {
    margin-top: 0.25em;
    margin-bottom: 0.25em;
    padding-left: 1rem;
}

.prose.prose-xs code {
    font-size: 0.6875rem;
}

.prose.prose-xs pre {
    font-size: 0.6875rem;
    padding: 0.5rem;
}

/* 响应式样式 */
@media (max-width: 768px) {
    .message-container {
        max-width: 90%;
    }

    .message-container.expanded {
        max-width: 95%;
    }

    .message-container.user-message {
        max-width: 85%;
    }

    .reference-item {
        padding: 8px;
    }

    .prose.prose-xs {
        font-size: 0.6875rem;
    }
}

@media (max-width: 480px) {
    .message-container {
        max-width: 95%;
    }

    .message-container.expanded {
        max-width: 98%;
    }

    .message-container.user-message {
        max-width: 90%;
    }

    .reference-item {
        padding: 6px;
    }
}