<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>置顶功能测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div id="app">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-2xl font-bold mb-6">置顶功能测试</h1>
            
            <!-- 登录区域 -->
            <div v-if="!token" class="bg-white p-6 rounded-lg shadow mb-6">
                <h2 class="text-lg font-semibold mb-4">登录</h2>
                <div class="flex gap-4">
                    <input v-model="username" placeholder="用户名" class="border p-2 rounded">
                    <input v-model="password" type="password" placeholder="密码" class="border p-2 rounded">
                    <button @click="login" class="bg-blue-500 text-white px-4 py-2 rounded">登录</button>
                </div>
            </div>
            
            <!-- 对话列表 -->
            <div v-if="token" class="bg-white p-6 rounded-lg shadow">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold">对话列表</h2>
                    <button @click="loadConversations" class="bg-green-500 text-white px-4 py-2 rounded">刷新</button>
                </div>
                
                <div v-if="conversations.length === 0" class="text-gray-500">
                    暂无对话
                </div>
                
                <div v-for="conversation in conversations" :key="conversation.id" 
                     class="border p-4 mb-2 rounded flex justify-between items-center">
                    <div>
                        <div class="flex items-center">
                            <i v-if="conversation.sticky_flag" class="fas fa-thumbtack text-yellow-500 mr-2"></i>
                            <span class="font-medium">{{ conversation.title }}</span>
                            <span v-if="conversation.sticky_flag" class="ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">置顶</span>
                        </div>
                        <div class="text-sm text-gray-500">
                            ID: {{ conversation.id }} | 
                            创建: {{ formatTime(conversation.created_at) }} | 
                            更新: {{ formatTime(conversation.updated_at) }}
                        </div>
                    </div>
                    <button @click="toggleSticky(conversation)" 
                            :class="[
                                'px-3 py-1 rounded text-sm',
                                conversation.sticky_flag 
                                    ? 'bg-yellow-500 text-white' 
                                    : 'bg-gray-200 text-gray-700'
                            ]">
                        {{ conversation.sticky_flag ? '取消置顶' : '置顶' }}
                    </button>
                </div>
            </div>
            
            <!-- 日志区域 -->
            <div v-if="logs.length > 0" class="bg-gray-800 text-white p-4 rounded-lg mt-6">
                <h3 class="text-lg font-semibold mb-2">操作日志</h3>
                <div v-for="(log, index) in logs" :key="index" class="text-sm mb-1">
                    {{ log }}
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref } = Vue;

        createApp({
            setup() {
                const username = ref('testuser');
                const password = ref('testpass');
                const token = ref('');
                const conversations = ref([]);
                const logs = ref([]);

                const addLog = (message) => {
                    const timestamp = new Date().toLocaleTimeString();
                    logs.value.unshift(`[${timestamp}] ${message}`);
                    console.log(message);
                };

                const formatTime = (timestamp) => {
                    return new Date(timestamp).toLocaleString();
                };

                const login = async () => {
                    try {
                        addLog('开始登录...');
                        const response = await fetch('/api/login', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                username: username.value,
                                password: password.value
                            })
                        });

                        if (response.ok) {
                            const result = await response.json();
                            token.value = result.data.access_token;
                            addLog('登录成功');
                            await loadConversations();
                        } else {
                            addLog('登录失败: ' + response.status);
                        }
                    } catch (error) {
                        addLog('登录错误: ' + error.message);
                    }
                };

                const loadConversations = async () => {
                    try {
                        addLog('加载对话列表...');
                        const response = await fetch('/api/conversations', {
                            headers: {
                                'Authorization': `Bearer ${token.value}`
                            }
                        });

                        if (response.ok) {
                            const result = await response.json();
                            conversations.value = result.data || result;
                            addLog(`加载了 ${conversations.value.length} 个对话`);
                            
                            // 显示每个对话的详细信息
                            conversations.value.forEach(conv => {
                                addLog(`对话 ${conv.id}: ${conv.title}, 置顶: ${conv.sticky_flag}`);
                            });
                        } else {
                            addLog('加载对话失败: ' + response.status);
                        }
                    } catch (error) {
                        addLog('加载对话错误: ' + error.message);
                    }
                };

                const toggleSticky = async (conversation) => {
                    try {
                        const newStickyFlag = !conversation.sticky_flag;
                        addLog(`切换对话 ${conversation.id} 置顶状态到: ${newStickyFlag}`);

                        const response = await fetch(`/api/conversations/${conversation.id}/sticky`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token.value}`
                            },
                            body: JSON.stringify({
                                sticky_flag: newStickyFlag
                            })
                        });

                        if (response.ok) {
                            const result = await response.json();
                            addLog('置顶状态更新成功');
                            addLog('API返回: ' + JSON.stringify(result.data));
                            
                            // 更新本地状态
                            conversation.sticky_flag = newStickyFlag;
                            conversation.updated_at = new Date().toISOString();
                            
                            // 重新加载列表
                            await loadConversations();
                        } else {
                            const errorText = await response.text();
                            addLog('置顶状态更新失败: ' + response.status + ' - ' + errorText);
                        }
                    } catch (error) {
                        addLog('置顶操作错误: ' + error.message);
                    }
                };

                return {
                    username,
                    password,
                    token,
                    conversations,
                    logs,
                    login,
                    loadConversations,
                    toggleSticky,
                    formatTime
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
