<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>置顶功能调试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div id="app">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-3xl font-bold mb-6">置顶功能调试页面</h1>
            
            <!-- 登录区域 -->
            <div v-if="!token" class="bg-white p-6 rounded-lg shadow mb-6">
                <h2 class="text-xl font-semibold mb-4">登录</h2>
                <div class="flex gap-4">
                    <input v-model="username" placeholder="用户名" class="border p-3 rounded-lg">
                    <input v-model="password" type="password" placeholder="密码" class="border p-3 rounded-lg">
                    <button @click="login" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600">登录</button>
                </div>
            </div>
            
            <!-- 主要内容区域 -->
            <div v-if="token" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 对话列表 -->
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold">对话列表</h2>
                        <button @click="loadConversations" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600">
                            <i class="fas fa-refresh mr-2"></i>刷新
                        </button>
                    </div>
                    
                    <!-- 分组显示 -->
                    <div v-for="group in groupedConversations" :key="group.title" class="mb-6">
                        <h3 class="text-lg font-medium text-gray-700 mb-3 border-b pb-2">{{ group.title }}</h3>
                        
                        <div v-for="conversation in group.conversations" :key="conversation.id" 
                             class="border rounded-lg p-4 mb-3 hover:bg-gray-50">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <i v-if="conversation.sticky_flag" class="fas fa-thumbtack text-yellow-500 mr-2"></i>
                                        <span class="font-medium text-lg">{{ conversation.title }}</span>
                                        <span v-if="conversation.sticky_flag" class="ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">置顶</span>
                                    </div>
                                    <div class="text-sm text-gray-600 space-y-1">
                                        <div>ID: {{ conversation.id }}</div>
                                        <div>创建时间: {{ formatTime(conversation.created_at) }}</div>
                                        <div>更新时间: {{ formatTime(conversation.updated_at) }}</div>
                                        <div>置顶状态: {{ conversation.sticky_flag ? '是' : '否' }}</div>
                                    </div>
                                </div>
                                <div class="flex flex-col gap-2">
                                    <button @click="toggleSticky(conversation)" 
                                            :class="[
                                                'px-4 py-2 rounded-lg text-sm font-medium transition-colors',
                                                conversation.sticky_flag 
                                                    ? 'bg-yellow-500 text-white hover:bg-yellow-600' 
                                                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                            ]">
                                        <i :class="[
                                            'fas mr-2',
                                            conversation.sticky_flag ? 'fa-thumbtack' : 'fa-thumbtack'
                                        ]"></i>
                                        {{ conversation.sticky_flag ? '取消置顶' : '置顶' }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div v-if="conversations.length === 0" class="text-center text-gray-500 py-8">
                        暂无对话
                    </div>
                </div>
                
                <!-- 调试信息 -->
                <div class="bg-gray-900 text-white p-6 rounded-lg shadow">
                    <h2 class="text-xl font-semibold mb-4">调试信息</h2>
                    <div class="space-y-2 text-sm font-mono">
                        <div>对话总数: {{ conversations.length }}</div>
                        <div>分组数量: {{ groupedConversations.length }}</div>
                        <div>当前用户: {{ currentUser }}</div>
                    </div>
                    
                    <h3 class="text-lg font-semibold mt-6 mb-3">原始对话数据</h3>
                    <pre class="bg-gray-800 p-3 rounded text-xs overflow-auto max-h-64">{{ JSON.stringify(conversations, null, 2) }}</pre>
                    
                    <h3 class="text-lg font-semibold mt-6 mb-3">分组后数据</h3>
                    <pre class="bg-gray-800 p-3 rounded text-xs overflow-auto max-h-64">{{ JSON.stringify(groupedConversations, null, 2) }}</pre>
                    
                    <h3 class="text-lg font-semibold mt-6 mb-3">操作日志</h3>
                    <div class="bg-gray-800 p-3 rounded text-xs max-h-64 overflow-auto">
                        <div v-for="(log, index) in logs" :key="index" class="mb-1">
                            {{ log }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, computed } = Vue;

        createApp({
            setup() {
                const username = ref('testuser');
                const password = ref('testpass');
                const token = ref('');
                const currentUser = ref('');
                const conversations = ref([]);
                const logs = ref([]);

                const addLog = (message) => {
                    const timestamp = new Date().toLocaleTimeString();
                    logs.value.unshift(`[${timestamp}] ${message}`);
                    console.log(message);
                };

                const formatTime = (timestamp) => {
                    return new Date(timestamp).toLocaleString();
                };

                // 计算属性：分组对话
                const groupedConversations = computed(() => {
                    addLog(`Computing groupedConversations, conversations count: ${conversations.value.length}`);
                    
                    const now = new Date();
                    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
                    
                    const groups = [
                        { title: '置顶对话', conversations: [] },
                        { title: '今天', conversations: [] },
                        { title: '最近一周', conversations: [] },
                        { title: '最近30天', conversations: [] },
                        { title: '更早', conversations: [] }
                    ];
                    
                    conversations.value.forEach(conversation => {
                        const updatedAt = new Date(conversation.updated_at);
                        
                        // 置顶对话优先
                        if (conversation.sticky_flag) {
                            groups[0].conversations.push(conversation);
                        } else if (updatedAt >= today) {
                            groups[1].conversations.push(conversation);
                        } else if (updatedAt >= weekAgo) {
                            groups[2].conversations.push(conversation);
                        } else if (updatedAt >= monthAgo) {
                            groups[3].conversations.push(conversation);
                        } else {
                            groups[4].conversations.push(conversation);
                        }
                    });
                    
                    // 只返回有对话的分组
                    const result = groups.filter(group => group.conversations.length > 0);
                    addLog(`Grouped conversations result: ${result.length} groups`);
                    return result;
                });

                const login = async () => {
                    try {
                        addLog('开始登录...');
                        const response = await fetch('/api/login', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                username: username.value,
                                password: password.value
                            })
                        });

                        if (response.ok) {
                            const result = await response.json();
                            token.value = result.data.access_token;
                            currentUser.value = result.data.user.username;
                            addLog('登录成功');
                            await loadConversations();
                        } else {
                            addLog('登录失败: ' + response.status);
                        }
                    } catch (error) {
                        addLog('登录错误: ' + error.message);
                    }
                };

                const loadConversations = async () => {
                    try {
                        addLog('加载对话列表...');
                        const response = await fetch('/api/conversations', {
                            headers: {
                                'Authorization': `Bearer ${token.value}`
                            }
                        });

                        if (response.ok) {
                            const result = await response.json();
                            addLog('API响应: ' + JSON.stringify(result));
                            
                            const data = result.data || result;
                            conversations.value = data.map(conv => ({
                                id: conv.id,
                                title: conv.title || `对话 ${conv.id}`,
                                created_at: conv.created_at,
                                updated_at: conv.updated_at || conv.created_at,
                                sticky_flag: conv.sticky_flag || false
                            }));
                            
                            addLog(`加载了 ${conversations.value.length} 个对话`);
                        } else {
                            addLog('加载对话失败: ' + response.status);
                        }
                    } catch (error) {
                        addLog('加载对话错误: ' + error.message);
                    }
                };

                const toggleSticky = async (conversation) => {
                    try {
                        const newStickyFlag = !conversation.sticky_flag;
                        addLog(`切换对话 ${conversation.id} 置顶状态到: ${newStickyFlag}`);

                        const response = await fetch(`/api/conversations/${conversation.id}/sticky`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token.value}`
                            },
                            body: JSON.stringify({
                                sticky_flag: newStickyFlag
                            })
                        });

                        if (response.ok) {
                            const result = await response.json();
                            addLog('置顶状态更新成功');
                            addLog('API返回: ' + JSON.stringify(result.data));
                            
                            // 重新加载列表
                            await loadConversations();
                        } else {
                            const errorText = await response.text();
                            addLog('置顶状态更新失败: ' + response.status + ' - ' + errorText);
                        }
                    } catch (error) {
                        addLog('置顶操作错误: ' + error.message);
                    }
                };

                return {
                    username,
                    password,
                    token,
                    currentUser,
                    conversations,
                    groupedConversations,
                    logs,
                    login,
                    loadConversations,
                    toggleSticky,
                    formatTime,
                    JSON
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
