<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Content-Type 测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div id="app">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold mb-6">Content-Type 测试页面</h1>
            
            <div class="bg-white p-6 rounded-lg shadow mb-6">
                <h2 class="text-xl font-semibold mb-4">登录</h2>
                <div class="space-y-4">
                    <input v-model="credentials.username" placeholder="用户名" 
                           class="w-full px-3 py-2 border rounded" value="test_user">
                    <input v-model="credentials.password" type="password" placeholder="密码"
                           class="w-full px-3 py-2 border rounded" value="test_password">
                    <button @click="login" :disabled="isLoading"
                            class="w-full bg-blue-500 text-white py-2 rounded hover:bg-blue-600 disabled:opacity-50">
                        {{ isLoading ? '登录中...' : '登录' }}
                    </button>
                </div>
            </div>
            
            <div v-if="token" class="bg-white p-6 rounded-lg shadow mb-6">
                <h2 class="text-xl font-semibold mb-4">Content-Type 测试</h2>
                <div class="space-y-4">
                    <button @click="testApiService" :disabled="isLoading"
                            class="w-full bg-green-500 text-white py-2 rounded hover:bg-green-600 disabled:opacity-50">
                        测试 ApiService (应该是 application/json)
                    </button>
                    <button @click="testFetchDirect" :disabled="isLoading"
                            class="w-full bg-purple-500 text-white py-2 rounded hover:bg-purple-600 disabled:opacity-50">
                        测试直接 fetch (手动设置 Content-Type)
                    </button>
                    <button @click="testFetchWithoutContentType" :disabled="isLoading"
                            class="w-full bg-orange-500 text-white py-2 rounded hover:bg-orange-600 disabled:opacity-50">
                        测试 fetch 不设置 Content-Type
                    </button>
                </div>
            </div>
            
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">测试日志</h2>
                <div class="h-96 overflow-y-auto border rounded p-2 bg-gray-50">
                    <div v-for="(log, index) in logs" :key="index" 
                         class="mb-2 p-2 border-l-4 border-blue-500 bg-white">
                        <div class="font-mono text-sm">{{ log.timestamp }}</div>
                        <div class="font-semibold">{{ log.title }}</div>
                        <div class="text-sm text-gray-600">{{ log.message }}</div>
                        <div v-if="log.details" class="text-xs text-gray-500 mt-1">
                            <pre>{{ log.details }}</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/utils.js"></script>
    <script src="js/api-service.js"></script>
    <script>
        const { createApp, ref } = Vue;

        createApp({
            setup() {
                const credentials = ref({
                    username: 'test_user',
                    password: 'test_password'
                });
                const token = ref('');
                const isLoading = ref(false);
                const logs = ref([]);

                const addLog = (title, message, details = null) => {
                    logs.value.unshift({
                        timestamp: new Date().toLocaleTimeString(),
                        title,
                        message,
                        details
                    });
                };

                const login = async () => {
                    isLoading.value = true;
                    addLog('登录测试', '开始登录...');
                    
                    try {
                        const result = await apiService.login(
                            credentials.value.username,
                            credentials.value.password
                        );
                        
                        if (result.success) {
                            token.value = apiService.token;
                            addLog('登录成功', `Token: ${token.value.substring(0, 20)}...`);
                        } else {
                            addLog('登录失败', result.message);
                        }
                    } catch (error) {
                        addLog('登录异常', error.message);
                    } finally {
                        isLoading.value = false;
                    }
                };

                const testApiService = async () => {
                    isLoading.value = true;
                    addLog('ApiService 测试', '使用 ApiService 创建对话...');
                    
                    try {
                        // 拦截 fetch 请求来查看实际的请求头
                        const originalFetch = window.fetch;
                        let requestHeaders = null;
                        
                        window.fetch = function(...args) {
                            const [url, options] = args;
                            if (url.includes('/conversations/new')) {
                                requestHeaders = options.headers;
                                addLog('请求拦截', '拦截到 /conversations/new 请求', 
                                    `Headers: ${JSON.stringify(requestHeaders, null, 2)}`);
                            }
                            return originalFetch.apply(this, args);
                        };
                        
                        const result = await apiService.createConversation('ApiService 测试对话');
                        
                        // 恢复原始 fetch
                        window.fetch = originalFetch;
                        
                        if (result.success) {
                            addLog('ApiService 成功', `对话ID: ${result.conversation.id}`);
                        } else {
                            addLog('ApiService 失败', result.message);
                        }
                    } catch (error) {
                        addLog('ApiService 异常', error.message);
                    } finally {
                        isLoading.value = false;
                    }
                };

                const testFetchDirect = async () => {
                    isLoading.value = true;
                    addLog('直接 Fetch 测试', '使用直接 fetch 创建对话...');
                    
                    try {
                        const response = await fetch('/api/conversations/new', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json; charset=utf-8',
                                'Authorization': `Bearer ${token.value}`
                            },
                            body: JSON.stringify({
                                title: '直接 Fetch 测试对话'
                            })
                        });
                        
                        addLog('请求头检查', '直接 fetch 请求头', 
                            `Content-Type: application/json; charset=utf-8`);
                        
                        const result = await response.json();
                        
                        if (response.ok && result.code === 200) {
                            addLog('直接 Fetch 成功', `对话ID: ${result.data.id}`);
                        } else {
                            addLog('直接 Fetch 失败', result.message || '未知错误');
                        }
                    } catch (error) {
                        addLog('直接 Fetch 异常', error.message);
                    } finally {
                        isLoading.value = false;
                    }
                };

                const testFetchWithoutContentType = async () => {
                    isLoading.value = true;
                    addLog('无 Content-Type 测试', '不设置 Content-Type...');
                    
                    try {
                        const response = await fetch('/api/conversations/new', {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${token.value}`
                            },
                            body: JSON.stringify({
                                title: '无 Content-Type 测试对话'
                            })
                        });
                        
                        addLog('请求头检查', '无 Content-Type 请求', 
                            '未设置 Content-Type，浏览器可能自动设置');
                        
                        const result = await response.json();
                        
                        if (response.ok && result.code === 200) {
                            addLog('无 Content-Type 成功', `对话ID: ${result.data.id}`);
                        } else {
                            addLog('无 Content-Type 失败', result.message || '未知错误');
                        }
                    } catch (error) {
                        addLog('无 Content-Type 异常', error.message);
                    } finally {
                        isLoading.value = false;
                    }
                };

                return {
                    credentials,
                    token,
                    isLoading,
                    logs,
                    login,
                    testApiService,
                    testFetchDirect,
                    testFetchWithoutContentType
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
