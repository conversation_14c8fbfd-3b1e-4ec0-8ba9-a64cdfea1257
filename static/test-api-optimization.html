<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API优化测试页面</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .log-entry {
            padding: 8px;
            margin: 4px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .log-success { background-color: #d4edda; color: #155724; }
        .log-error { background-color: #f8d7da; color: #721c24; }
        .log-info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div id="app">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold mb-6">API优化测试页面</h1>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 测试按钮区域 -->
                <div class="bg-white p-6 rounded-lg shadow">
                    <h2 class="text-xl font-semibold mb-4">API测试</h2>
                    
                    <div class="space-y-4">
                        <div>
                            <h3 class="font-medium mb-2">认证测试</h3>
                            <div class="space-y-2">
                                <input v-model="testCredentials.username" placeholder="用户名" 
                                       class="w-full px-3 py-2 border rounded">
                                <input v-model="testCredentials.password" type="password" placeholder="密码"
                                       class="w-full px-3 py-2 border rounded">
                                <button @click="testLogin" :disabled="isLoading"
                                        class="w-full bg-blue-500 text-white py-2 rounded hover:bg-blue-600 disabled:opacity-50">
                                    {{ isLoading ? '测试中...' : '测试登录' }}
                                </button>
                            </div>
                        </div>
                        
                        <div v-if="isAuthenticated">
                            <h3 class="font-medium mb-2">对话管理测试</h3>
                            <div class="space-y-2">
                                <button @click="testGetConversations" :disabled="isLoading"
                                        class="w-full bg-green-500 text-white py-2 rounded hover:bg-green-600 disabled:opacity-50">
                                    获取对话列表
                                </button>
                                <button @click="testCreateConversation" :disabled="isLoading"
                                        class="w-full bg-purple-500 text-white py-2 rounded hover:bg-purple-600 disabled:opacity-50">
                                    创建新对话
                                </button>
                            </div>
                        </div>
                        
                        <div v-if="isAuthenticated && currentConversationId">
                            <h3 class="font-medium mb-2">消息测试</h3>
                            <div class="space-y-2">
                                <button @click="testGetMessages" :disabled="isLoading"
                                        class="w-full bg-yellow-500 text-white py-2 rounded hover:bg-yellow-600 disabled:opacity-50">
                                    获取消息
                                </button>
                                <input v-model="testMessage" placeholder="测试消息" 
                                       class="w-full px-3 py-2 border rounded">
                                <button @click="testSendMessage" :disabled="isLoading || !testMessage"
                                        class="w-full bg-red-500 text-white py-2 rounded hover:bg-red-600 disabled:opacity-50">
                                    发送流式消息
                                </button>
                            </div>
                        </div>
                        
                        <button @click="clearLogs"
                                class="w-full bg-gray-500 text-white py-2 rounded hover:bg-gray-600">
                            清空日志
                        </button>
                    </div>
                </div>
                
                <!-- 日志显示区域 -->
                <div class="bg-white p-6 rounded-lg shadow">
                    <h2 class="text-xl font-semibold mb-4">测试日志</h2>
                    <div class="h-96 overflow-y-auto border rounded p-2">
                        <div v-for="(log, index) in logs" :key="index" 
                             :class="['log-entry', `log-${log.type}`]">
                            <span class="font-bold">{{ log.timestamp }}</span> - {{ log.message }}
                        </div>
                        <div v-if="logs.length === 0" class="text-gray-500 text-center py-8">
                            暂无日志
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 状态显示 -->
            <div class="mt-6 bg-white p-4 rounded-lg shadow">
                <h3 class="font-semibold mb-2">当前状态</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                        <span class="font-medium">认证状态:</span>
                        <span :class="isAuthenticated ? 'text-green-600' : 'text-red-600'">
                            {{ isAuthenticated ? '已认证' : '未认证' }}
                        </span>
                    </div>
                    <div>
                        <span class="font-medium">对话数量:</span>
                        <span class="text-blue-600">{{ conversations.length }}</span>
                    </div>
                    <div>
                        <span class="font-medium">当前对话:</span>
                        <span class="text-purple-600">{{ currentConversationId || '无' }}</span>
                    </div>
                    <div>
                        <span class="font-medium">消息数量:</span>
                        <span class="text-orange-600">{{ messages.length }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/utils.js"></script>
    <script src="js/api-service.js"></script>
    <script>
        const { createApp, ref } = Vue;

        createApp({
            setup() {
                const logs = ref([]);
                const isLoading = ref(false);
                const isAuthenticated = ref(false);
                const conversations = ref([]);
                const currentConversationId = ref(null);
                const messages = ref([]);
                
                const testCredentials = ref({
                    username: 'test_user',
                    password: 'test_password'
                });
                const testMessage = ref('这是一个测试消息');

                // 日志记录函数
                const addLog = (message, type = 'info') => {
                    logs.value.push({
                        timestamp: new Date().toLocaleTimeString(),
                        message,
                        type
                    });
                };

                // 清空日志
                const clearLogs = () => {
                    logs.value = [];
                };

                // 测试登录
                const testLogin = async () => {
                    isLoading.value = true;
                    addLog('开始测试登录...', 'info');
                    
                    try {
                        const result = await apiService.login(
                            testCredentials.value.username,
                            testCredentials.value.password
                        );
                        
                        if (result.success) {
                            isAuthenticated.value = true;
                            addLog(`登录成功: ${result.user.username}`, 'success');
                        } else {
                            addLog(`登录失败: ${result.message}`, 'error');
                        }
                    } catch (error) {
                        addLog(`登录异常: ${error.message}`, 'error');
                    } finally {
                        isLoading.value = false;
                    }
                };

                // 测试获取对话列表
                const testGetConversations = async () => {
                    isLoading.value = true;
                    addLog('开始获取对话列表...', 'info');
                    
                    try {
                        const result = await apiService.getConversations();
                        
                        if (result.success) {
                            conversations.value = result.conversations;
                            if (result.conversations.length > 0) {
                                currentConversationId.value = result.conversations[0].id;
                            }
                            addLog(`获取对话列表成功: ${result.conversations.length} 个对话`, 'success');
                        } else {
                            addLog(`获取对话列表失败: ${result.message}`, 'error');
                        }
                    } catch (error) {
                        addLog(`获取对话列表异常: ${error.message}`, 'error');
                    } finally {
                        isLoading.value = false;
                    }
                };

                // 测试创建对话
                const testCreateConversation = async () => {
                    isLoading.value = true;
                    addLog('开始创建新对话...', 'info');
                    
                    try {
                        const result = await apiService.createConversation('测试对话');
                        
                        if (result.success) {
                            conversations.value.unshift(result.conversation);
                            currentConversationId.value = result.conversation.id;
                            addLog(`创建对话成功: ID ${result.conversation.id}`, 'success');
                        } else {
                            addLog(`创建对话失败: ${result.message}`, 'error');
                        }
                    } catch (error) {
                        addLog(`创建对话异常: ${error.message}`, 'error');
                    } finally {
                        isLoading.value = false;
                    }
                };

                // 测试获取消息
                const testGetMessages = async () => {
                    isLoading.value = true;
                    addLog(`开始获取对话 ${currentConversationId.value} 的消息...`, 'info');
                    
                    try {
                        const result = await apiService.getMessages(currentConversationId.value);
                        
                        if (result.success) {
                            messages.value = result.messages;
                            addLog(`获取消息成功: ${result.messages.length} 条消息`, 'success');
                        } else {
                            addLog(`获取消息失败: ${result.message}`, 'error');
                        }
                    } catch (error) {
                        addLog(`获取消息异常: ${error.message}`, 'error');
                    } finally {
                        isLoading.value = false;
                    }
                };

                // 测试发送流式消息
                const testSendMessage = async () => {
                    isLoading.value = true;
                    addLog(`开始发送流式消息: "${testMessage.value}"`, 'info');
                    
                    let receivedContent = '';
                    
                    try {
                        await apiService.sendStreamMessage(
                            currentConversationId.value,
                            testMessage.value,
                            {
                                onStart: () => {
                                    addLog('流式响应开始', 'info');
                                },
                                onData: (content) => {
                                    receivedContent += content;
                                    addLog(`接收到内容: "${content}"`, 'success');
                                },
                                onError: (error) => {
                                    addLog(`流式响应错误: ${error}`, 'error');
                                },
                                onComplete: () => {
                                    addLog(`流式响应完成，总共接收: ${receivedContent.length} 字符`, 'success');
                                }
                            }
                        );
                    } catch (error) {
                        addLog(`发送消息异常: ${error.message}`, 'error');
                    } finally {
                        isLoading.value = false;
                    }
                };

                // 检查初始认证状态
                isAuthenticated.value = apiService.isAuthenticated();
                if (isAuthenticated.value) {
                    addLog('检测到已有认证状态', 'success');
                }

                return {
                    logs,
                    isLoading,
                    isAuthenticated,
                    conversations,
                    currentConversationId,
                    messages,
                    testCredentials,
                    testMessage,
                    addLog,
                    clearLogs,
                    testLogin,
                    testGetConversations,
                    testCreateConversation,
                    testGetMessages,
                    testSendMessage
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
