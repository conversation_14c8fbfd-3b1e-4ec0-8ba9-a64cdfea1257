<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式响应测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .response-area {
            background-color: #f5f5f5;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 10px;
            min-height: 200px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .controls {
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <h1>流式响应测试</h1>
    
    <div class="container">
        <h3>配置</h3>
        <div>
            <label>Token: </label>
            <input type="text" id="token" style="width: 100%; margin: 5px 0;" 
                   value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ3YW5nemhpeGluIiwidHlwZSI6ImFjY2Vzc190b2tlbiIsImlhdCI6MTc1Mzc1Njg4MSwiZXhwIjoxNzUzNzYwNDgxfQ.sMJJkGuXkfOnkkFCKf1r3o9SJjmA8a99-UARkwMNU00">
        </div>
        <div>
            <label>对话ID: </label>
            <input type="number" id="conversationId" value="1753608474944" style="width: 200px; margin: 5px 0;">
        </div>
        <div>
            <label>消息: </label>
            <input type="text" id="message" value="你好，请介绍一下自己" style="width: 100%; margin: 5px 0;">
        </div>
    </div>
    
    <div class="container">
        <h3>控制</h3>
        <div class="controls">
            <button onclick="testStream()" id="testBtn">测试流式响应</button>
            <button onclick="clearResponse()">清空响应</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>
    
    <div class="container">
        <h3>流式响应内容</h3>
        <div id="response" class="response-area"></div>
    </div>
    
    <div class="container">
        <h3>调试日志</h3>
        <div id="log" class="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : (type === 'success' ? 'success' : '');
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearResponse() {
            document.getElementById('response').textContent = '';
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        async function testStream() {
            const token = document.getElementById('token').value;
            const conversationId = parseInt(document.getElementById('conversationId').value);
            const message = document.getElementById('message').value;
            const responseDiv = document.getElementById('response');
            const testBtn = document.getElementById('testBtn');
            
            if (!token || !conversationId || !message) {
                log('请填写所有必需字段', 'error');
                return;
            }
            
            testBtn.disabled = true;
            clearResponse();
            log('开始测试流式响应...');
            
            try {
                const response = await fetch('/api/chat/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        conversation_id: conversationId,
                        message: message,
                        collection_name: 'FinancialResearchOffice',
                        input: {}
                    })
                });
                
                log(`HTTP状态: ${response.status}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder('utf-8');
                let buffer = '';
                let fullResponse = '';
                
                log('开始读取流式数据...');
                
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        log('流式数据读取完成');
                        break;
                    }
                    
                    const chunk = decoder.decode(value, { stream: true });
                    buffer += chunk;
                    log(`接收到数据块: ${chunk.length} 字节`);
                    
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || '';
                    
                    for (const line of lines) {
                        const trimmedLine = line.trim();
                        if (trimmedLine.startsWith('data: ')) {
                            const data = trimmedLine.substring(6).trim();
                            log(`SSE数据: ${data}`);
                            
                            if (data === '[DONE]') {
                                log('收到结束标记', 'success');
                                break;
                            }
                            
                            try {
                                const parsedData = JSON.parse(data);
                                log(`解析后的数据: ${JSON.stringify(parsedData)}`);
                                
                                if (parsedData.code === 200 && parsedData.data?.content) {
                                    fullResponse += parsedData.data.content;
                                    responseDiv.textContent = fullResponse;
                                    log(`添加内容: "${parsedData.data.content}"`, 'success');
                                } else if (parsedData.code !== 200) {
                                    log(`错误响应: ${JSON.stringify(parsedData)}`, 'error');
                                    responseDiv.textContent += `\n错误: ${parsedData.data?.error || parsedData.message}`;
                                }
                            } catch (e) {
                                log(`JSON解析失败: ${e.message}, 原始数据: ${data}`, 'error');
                            }
                        }
                    }
                }
                
                // 处理缓冲区剩余数据
                if (buffer.trim()) {
                    const trimmedLine = buffer.trim();
                    if (trimmedLine.startsWith('data: ')) {
                        const data = trimmedLine.substring(6).trim();
                        if (data !== '[DONE]') {
                            try {
                                const parsedData = JSON.parse(data);
                                if (parsedData.code === 200 && parsedData.data?.content) {
                                    fullResponse += parsedData.data.content;
                                    responseDiv.textContent = fullResponse;
                                }
                            } catch (e) {
                                log(`缓冲区数据解析失败: ${e.message}`, 'error');
                            }
                        }
                    }
                }
                
                log('测试完成', 'success');
                
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
                responseDiv.textContent = `错误: ${error.message}`;
            } finally {
                testBtn.disabled = false;
            }
        }
    </script>
</body>
</html>
