<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>银通Wiki</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-gray-100">
    <div id="app" class="h-screen">
        <!-- 聊天主页面 -->
        <div class="flex flex-col h-full">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow">
                <div class="flex items-center justify-between px-4 py-3">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <img src="images/logo.png" alt="Logo" class="h-8 w-8">
                        </div>
                        <div class="ml-4">
                            <h1 class="text-xl font-bold text-gray-900">银通Wiki</h1>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <div class="mr-4 text-sm text-gray-700 bg-gray-100 rounded-full px-4 py-1.5">
                            欢迎, {{ currentUser }}
                        </div>
                        <button @click="handleLogout"
                            class="flex items-center text-sm text-gray-700 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 rounded-full px-4 py-1.5 transition duration-200">
                            <i class="fas fa-sign-out-alt mr-1"></i> 退出
                        </button>
                    </div>
                </div>
            </header>

            <!-- 主体内容 -->
            <div class="flex flex-1 overflow-hidden">
                <!-- 左侧面板 -->
                <div :class="['flex flex-col', isSidebarCollapsed ? 'w-20' : 'w-80', 'bg-white border-r border-gray-200 rounded-r-2xl shadow-lg']">
                    <!-- 面板头部 -->
                    <div class="flex items-center justify-between p-4 border-b border-gray-200 rounded-tr-2xl">
                        <div v-if="!isSidebarCollapsed" class="flex items-center">
                            <button @click="createNewConversation"
                                class="flex items-center justify-center w-full py-2 px-3 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 focus:outline-none transition duration-200">
                                <i class="fas fa-plus mr-2"></i>
                                新建对话
                            </button>
                        </div>
                        <button @click="toggleSidebar"
                            class="p-2 rounded-full hover:bg-gray-100 focus:outline-none transition duration-200">
                            <i :class="[isSidebarCollapsed ? 'fas fa-arrow-right' : 'fas fa-arrow-left', 'text-gray-600']"></i>
                        </button>
                    </div>

                    <!-- 新建对话按钮 -->
                    <div v-if="!isSidebarCollapsed" class="p-4 border-b border-gray-200 hidden">
                        <button @click="createNewConversation"
                            class="flex items-center justify-center w-full py-3 px-4 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 focus:outline-none transition duration-200">
                            <i class="fas fa-plus mr-2"></i>
                            <span v-if="!isSidebarCollapsed">新建对话</span>
                        </button>
                    </div>

                    <!-- 对话列表 -->
                    <div class="flex-1 overflow-y-auto">
                        <!-- 时间分组显示 -->
                        <div v-for="group in groupedConversations" :key="group.title">
                            <!-- 分组标题 -->
                            <div v-if="!isSidebarCollapsed && group.conversations.length > 0"
                                 class="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200">
                                {{ group.title }}
                            </div>

                            <!-- 分组内的对话 -->
                            <div v-for="conversation in group.conversations" :key="conversation.id"
                                @click="selectConversation(conversation.id)"
                                :class="[
                                    'p-4 border-b border-gray-200 cursor-pointer hover:bg-gray-50 flex items-center justify-between mx-2 my-1 rounded-xl transition duration-200',
                                    currentConversationId === conversation.id ? 'bg-blue-50 border-l-4 border-l-blue-500 rounded-l-none' : '',
                                    conversation.sticky_flag ? 'bg-yellow-50 border-l-4 border-l-yellow-400' : ''
                                ]">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center">
                                        <!-- 置顶图标 -->
                                        <i v-if="!isSidebarCollapsed && conversation.sticky_flag"
                                           class="fas fa-thumbtack text-yellow-500 text-xs mr-2"></i>
                                        <p v-if="!isSidebarCollapsed" class="text-sm font-medium text-gray-900 truncate">
                                            {{ conversation.title }}
                                        </p>
                                    </div>
                                    <p v-if="!isSidebarCollapsed" class="text-xs text-gray-500 truncate mt-1">
                                        {{ formatTime(conversation.updated_at) }}
                                    </p>
                                    <div v-if="isSidebarCollapsed" class="flex justify-center">
                                        <i :class="[
                                            'fas fa-comment',
                                            conversation.sticky_flag ? 'text-yellow-500' : 'text-gray-500'
                                        ]"></i>
                                    </div>
                                </div>
                                <div v-if="!isSidebarCollapsed && conversations.length > 1" class="relative">
                                    <button @click.stop="toggleConversationMenu(conversation.id)"
                                        class="ml-2 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition duration-200">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <!-- 菜单弹窗 -->
                                    <div v-if="openConversationMenuId === conversation.id"
                                         class="absolute right-0 top-8 mt-2 w-40 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200 conversation-menu">
                                        <button @click.stop="pinConversation(conversation.id)"
                                            class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i :class="[
                                                'fas mr-2',
                                                conversation.sticky_flag ? 'fa-thumbtack text-yellow-500' : 'fa-thumbtack'
                                            ]"></i>
                                            {{ conversation.sticky_flag ? '取消置顶' : '置顶该对话' }}
                                        </button>
                                        <button @click.stop="deleteConversation(conversation.id)"
                                            class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                            <i class="fas fa-trash mr-2"></i>
                                            删除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧聊天区域 -->
                <div class="flex flex-col flex-1 bg-gradient-to-br from-gray-50 to-gray-100">
                    <!-- 消息显示区域 -->
                    <div ref="messagesContainer" class="messages-container overflow-y-auto p-6 rounded-2xl m-4 bg-white shadow">
                        <div v-if="!currentConversationId" class="flex items-center justify-center h-full">
                            <div class="text-center">
                                <div class="bg-gray-100 rounded-full p-4 w-24 h-24 flex items-center justify-center mx-auto mb-6">
                                    <i class="fas fa-robot text-4xl text-gray-300"></i>
                                </div>
                                <h3 class="text-xl font-medium text-gray-500 mb-6">选择或创建一个对话开始聊天</h3>
                                <button @click="createNewConversation"
                                    class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-xl shadow-sm text-white bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 focus:outline-none transition duration-200">
                                    <i class="fas fa-plus mr-2"></i> 新建对话
                                </button>
                            </div>
                        </div>
                        <div v-else>
                            <transition-group name="slide-fade" tag="div">
                                <div v-for="(message, index) in messages" :key="index" class="mb-4">
                                    <div :class="[
                                        'max-w-3/4 px-4 py-3 rounded-2xl shadow-sm',
                                        message.role === 'user'
                                            ? 'ml-auto bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-br-none'
                                            : 'mr-auto bg-gray-100 text-gray-800 rounded-bl-none'
                                    ]" style="width: fit-content;">
                                        <!-- 消息内容 -->
                                        <div v-if="message.role === 'user'" class="whitespace-pre-wrap">{{ message.content }}</div>
                                        <div v-else v-html="renderMarkdown(message.content)" class="prose prose-sm max-w-none"></div>

                                        <!-- 引用内容（仅AI消息显示） -->
                                        <div v-if="message.role === 'assistant' && message.references && message.references.length > 0" class="mt-3 border-t border-gray-200 pt-3">
                                            <div class="flex items-center justify-between cursor-pointer" @click="toggleReferences(index)">
                                                <span class="text-sm font-medium text-gray-600">
                                                    <i class="fas fa-book-open mr-1"></i>
                                                    参考资料 ({{ message.references.length }})
                                                </span>
                                                <i :class="[
                                                    'fas text-gray-400 transition-transform duration-200',
                                                    message.referencesExpanded ? 'fa-chevron-up' : 'fa-chevron-down'
                                                ]"></i>
                                            </div>

                                            <!-- 引用内容列表 -->
                                            <div v-if="message.referencesExpanded" class="mt-2 space-y-2">
                                                <div v-for="(ref, refIndex) in message.references" :key="refIndex"
                                                     class="bg-white border border-gray-200 rounded-lg p-3 text-sm">
                                                    <div class="flex items-center justify-between mb-2">
                                                        <span class="font-medium text-gray-700">{{ ref.title }}</span>
                                                        <span class="text-xs text-gray-500">相关度: {{ (ref.score * 100).toFixed(1) }}%</span>
                                                    </div>
                                                    <div class="text-gray-600 text-xs leading-relaxed" v-html="renderMarkdown(ref.content)"></div>
                                                    <div v-if="ref.source" class="mt-2">
                                                        <a :href="ref.source" target="_blank" class="text-blue-500 hover:text-blue-700 text-xs">
                                                            <i class="fas fa-external-link-alt mr-1"></i>查看来源
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 折叠状态下的预览 -->
                                            <div v-else class="mt-2 text-xs text-gray-500">
                                                <div v-for="(ref, refIndex) in message.references.slice(0, 2)" :key="refIndex" class="truncate">
                                                    {{ ref.title }} - {{ ref.content.substring(0, 50) }}...
                                                </div>
                                                <div v-if="message.references.length > 2" class="text-gray-400">
                                                    还有 {{ message.references.length - 2 }} 个引用...
                                                </div>
                                            </div>
                                        </div>

                                        <div class="text-xs mt-1 opacity-70">
                                            {{ formatTime(message.timestamp) }}
                                        </div>
                                    </div>
                                </div>
                            </transition-group>
                            
                            <!-- 加载状态指示器 -->
                            <div v-if="isReceiving" class="mb-4">
                                <div class="mr-auto bg-gray-100 text-gray-800 rounded-2xl rounded-bl-none px-4 py-3 shadow-sm" style="width: fit-content;">
                                    <div class="flex items-center">
                                        <i class="fas fa-circle text-xs mr-1 animate-pulse text-blue-400"></i>
                                        <i class="fas fa-circle text-xs mr-1 animate-pulse text-blue-400" style="animation-delay: 0.2s"></i>
                                        <i class="fas fa-circle text-xs animate-pulse text-blue-400" style="animation-delay: 0.4s"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 输入区域 -->
                    <div v-if="currentConversationId" class="px-4 pb-6">
                        <div class="flex bg-white rounded-2xl shadow-lg p-2">
                            <div class="flex-1 mr-2">
                                <textarea v-model="newMessage" @keydown="handleKeyDown" ref="messageInput"
                                    placeholder="输入消息..." rows="2"
                                    class="block w-full px-4 py-3 border-0 rounded-xl placeholder-gray-400 focus:outline-none focus:ring-0 resize-none"></textarea>
                            </div>
                            <div class="flex items-end">
                                <button @click="sendMessage" :disabled="!newMessage.trim() || isSending"
                                    class="flex items-center justify-center h-12 w-12 rounded-xl shadow-sm text-white bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 focus:outline-none disabled:opacity-50 transition duration-200">
                                    <i :class="[isSending ? 'fas fa-spinner fa-spin' : 'fas fa-paper-plane']"></i>
                                </button>
                            </div>
                        </div>
                        <div class="mt-2 text-xs text-gray-500 text-center">
                            Enter发送消息，Shift+Enter换行
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/utils.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/chat.js"></script>
</body>
</html>