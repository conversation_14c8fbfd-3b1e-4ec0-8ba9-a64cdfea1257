/**
 * 前端工具函数库
 * 包含错误处理、调试、格式化等通用功能
 */

// 错误处理工具
const ErrorHandler = {
    // 全局错误处理
    setupGlobalErrorHandler() {
        window.addEventListener('error', (event) => {
            console.error('Global Error:', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
            
            // 在开发环境显示错误提示
            if (window.AppConfig && !window.AppConfig.features.enableErrorReporting) {
                this.showErrorToast(`错误: ${event.message}`);
            }
        });

        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled Promise Rejection:', event.reason);
            
            // 在开发环境显示错误提示
            if (window.AppConfig && !window.AppConfig.features.enableErrorReporting) {
                this.showErrorToast(`Promise错误: ${event.reason}`);
            }
        });
    },

    // 显示错误提示
    showErrorToast(message, duration = 3000) {
        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded shadow-lg z-50 transition-opacity';
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        // 自动移除
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    },

    // 显示成功提示
    showSuccessToast(message, duration = 3000) {
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded shadow-lg z-50 transition-opacity';
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    },

    // API错误处理
    handleApiError(error, context = '') {
        console.error(`API Error ${context}:`, error);
        
        let message = '网络请求失败';
        if (error.message) {
            message = error.message;
        } else if (typeof error === 'string') {
            message = error;
        }
        
        this.showErrorToast(message);
        return message;
    }
};

// 调试工具
const DebugUtils = {
    // 分级日志
    log(message, data = null) {
        if (window.AppConfig?.features?.enableDebugMode) {
            console.log(`[LOG] ${message}`, data);
        }
    },

    warn(message, data = null) {
        if (window.AppConfig?.features?.enableDebugMode) {
            console.warn(`[WARN] ${message}`, data);
        }
    },

    error(message, data = null) {
        console.error(`[ERROR] ${message}`, data);
    },

    // 性能计时
    time(label) {
        if (window.AppConfig?.features?.enableDebugMode) {
            console.time(label);
        }
    },

    timeEnd(label) {
        if (window.AppConfig?.features?.enableDebugMode) {
            console.timeEnd(label);
        }
    }
};

// 格式化工具
const FormatUtils = {
    // 智能时间格式化
    formatTime(timestamp) {
        if (!timestamp) return '';
        
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        // 小于1分钟
        if (diff < 60000) {
            return '刚刚';
        }
        
        // 小于1小时
        if (diff < 3600000) {
            const minutes = Math.floor(diff / 60000);
            return `${minutes}分钟前`;
        }
        
        // 小于24小时
        if (diff < 86400000) {
            const hours = Math.floor(diff / 3600000);
            return `${hours}小时前`;
        }
        
        // 小于7天
        if (diff < 604800000) {
            const days = Math.floor(diff / 86400000);
            return `${days}天前`;
        }
        
        // 超过7天显示具体日期
        return date.toLocaleDateString('zh-CN');
    },

    // 截断文本
    truncateText(text, maxLength = 50) {
        if (!text || text.length <= maxLength) {
            return text;
        }
        return text.substring(0, maxLength) + '...';
    }
};

// 性能监控工具
const PerformanceUtils = {
    marks: new Map(),

    // 标记时间点
    mark(name) {
        this.marks.set(name, performance.now());
    },

    // 测量耗时
    measure(startMark, endMark = null) {
        const startTime = this.marks.get(startMark);
        if (!startTime) {
            console.warn(`Performance mark "${startMark}" not found`);
            return 0;
        }

        const endTime = endMark ? this.marks.get(endMark) : performance.now();
        const duration = endTime - startTime;

        DebugUtils.log(`Performance: ${startMark} took ${duration.toFixed(2)}ms`);
        return duration;
    }
};

// API工具
const ApiUtils = {
    // 标准化API响应处理
    async handleResponse(response) {
        try {
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || `HTTP ${response.status}`);
            }

            // 检查业务状态码
            if (data.code && data.code !== 200) {
                throw new Error(data.message || '请求失败');
            }

            return data;
        } catch (error) {
            if (error instanceof SyntaxError) {
                throw new Error('服务器响应格式错误');
            }
            throw error;
        }
    },

    // 带重试的fetch
    async fetchWithRetry(url, options = {}, maxRetries = 3) {
        let lastError;

        for (let i = 0; i <= maxRetries; i++) {
            try {
                const response = await fetch(url, options);
                return await this.handleResponse(response);
            } catch (error) {
                lastError = error;

                if (i < maxRetries) {
                    // 指数退避
                    const delay = Math.pow(2, i) * 1000;
                    await new Promise(resolve => setTimeout(resolve, delay));
                    DebugUtils.warn(`API请求重试 ${i + 1}/${maxRetries}:`, { url, error: error.message });
                }
            }
        }

        throw lastError;
    },

    // 统一的API调用方法
    async request(url, options = {}) {
        // 合并headers，确保Content-Type不会被覆盖
        const headers = {
            'Content-Type': 'application/json',
            ...options.headers
        };

        const mergedOptions = { 
            ...options,
            headers
        };

        try {
            const data = await this.fetchWithRetry(url, mergedOptions);
            return {
                success: true,
                data: data.data,
                message: data.message,
                code: data.code
            };
        } catch (error) {
            ErrorHandler.handleApiError(error, url);
            return {
                success: false,
                data: null,
                message: error.message,
                error: error
            };
        }
    },

    // GET请求
    async get(url, options = {}) {
        return this.request(url, { ...options, method: 'GET' });
    },

    // POST请求
    async post(url, data = null, options = {}) {
        const postOptions = {
            ...options,
            method: 'POST',
            body: data ? JSON.stringify(data) : null
        };
        return this.request(url, postOptions);
    },

    // PUT请求
    async put(url, data = null, options = {}) {
        const putOptions = {
            ...options,
            method: 'PUT',
            body: data ? JSON.stringify(data) : null
        };
        return this.request(url, putOptions);
    },

    // DELETE请求
    async delete(url, options = {}) {
        return this.request(url, { ...options, method: 'DELETE' });
    },

    // 带认证的请求
    withAuth(token) {
        return {
            get: (url, options = {}) => this.get(url, {
                ...options,
                headers: { ...options.headers, 'Authorization': `Bearer ${token}` }
            }),
            post: (url, data, options = {}) => this.post(url, data, {
                ...options,
                headers: { ...options.headers, 'Authorization': `Bearer ${token}` }
            }),
            put: (url, data, options = {}) => this.put(url, data, {
                ...options,
                headers: { ...options.headers, 'Authorization': `Bearer ${token}` }
            }),
            delete: (url, options = {}) => this.delete(url, {
                ...options,
                headers: { ...options.headers, 'Authorization': `Bearer ${token}` }
            }),
            stream: (url, data, callbacks) => this.handleStreamResponse(url, data, token, callbacks)
        };
    },

    // 流式响应处理
    async handleStreamResponse(url, data, token, callbacks = {}) {
        const {
            onData = () => {},
            onError = () => {},
            onComplete = () => {},
            onStart = () => {},
            onReferences = () => {}  // 新增：处理引用内容的回调
        } = callbacks;

        try {
            onStart();

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(data)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder('utf-8');
            let buffer = '';

            while (true) {
                const { done, value } = await reader.read();

                if (done) {
                    break;
                }

                // 解码数据并添加到缓冲区
                const chunk = decoder.decode(value, { stream: true });
                buffer += chunk;

                // 按行分割数据，保留最后一个可能不完整的行
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    const trimmedLine = line.trim();
                    if (trimmedLine.startsWith('data: ')) {
                        const dataStr = trimmedLine.substring(6).trim();

                        if (dataStr === '[DONE]') {
                            onComplete();
                            return;
                        }

                        try {
                            const parsedData = JSON.parse(dataStr);

                            if (parsedData.code === 200 && parsedData.data) {
                                // 处理不同类型的数据
                                if (parsedData.data.type === 'references' && parsedData.data.references) {
                                    onReferences(parsedData.data.references, parsedData);
                                } else if (parsedData.data.type === 'content' && parsedData.data.content) {
                                    onData(parsedData.data.content, parsedData);
                                } else if (parsedData.data.content) {
                                    // 向后兼容：没有type字段的情况
                                    onData(parsedData.data.content, parsedData);
                                }
                            } else if (parsedData.code !== 200) {
                                onError(parsedData.data?.error || parsedData.message, parsedData);
                                return;
                            }
                        } catch (e) {
                            console.error('解析流数据失败:', e, 'Raw data:', dataStr);
                        }
                    }
                }
            }

            // 处理缓冲区中剩余的数据
            if (buffer.trim()) {
                const trimmedLine = buffer.trim();
                if (trimmedLine.startsWith('data: ')) {
                    const dataStr = trimmedLine.substring(6).trim();
                    if (dataStr !== '[DONE]') {
                        try {
                            const parsedData = JSON.parse(dataStr);
                            if (parsedData.code === 200 && parsedData.data) {
                                // 处理不同类型的数据
                                if (parsedData.data.type === 'references' && parsedData.data.references) {
                                    onReferences(parsedData.data.references, parsedData);
                                } else if (parsedData.data.type === 'content' && parsedData.data.content) {
                                    onData(parsedData.data.content, parsedData);
                                } else if (parsedData.data.content) {
                                    // 向后兼容：没有type字段的情况
                                    onData(parsedData.data.content, parsedData);
                                }
                            }
                        } catch (e) {
                            console.error('解析缓冲区数据失败:', e);
                        }
                    }
                }
            }

            onComplete();

        } catch (error) {
            console.error('流式请求失败:', error);
            onError(error.message, error);
        }
    }
};
