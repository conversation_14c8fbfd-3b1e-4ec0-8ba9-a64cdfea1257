const { createApp } = Vue;
const apiService = new ApiService();

// 创建Vue应用
const app = createApp({
    data() {
        return {
            conversations: [],
            currentConversation: null,
            messages: [],
            userInput: '',
            isGenerating: false,
            isTemporaryConversation: false
        };
    },

    async mounted() {
        // 页面加载时获取会话列表
        await this.loadConversations();
        
        // 如果有会话，选择第一个
        if (this.conversations.length > 0) {
            this.selectConversation(this.conversations[0]);
        }
    },

    methods: {
        // 加载会话列表
        async loadConversations() {
            try {
                this.conversations = await apiService.getConversations();
            } catch (error) {
                console.error('加载会话失败:', error);
                alert('加载会话失败: ' + error.message);
            }
        },

        // 创建新会话
        async createNewConversation() {
            // 创建临时会话对象
            const tempConversation = {
                id: Date.now(), // 使用时间戳作为临时ID
                title: '新的对话',
                created_at: new Date().toISOString()
            };

            // 设置为当前会话
            this.currentConversation = tempConversation;
            this.messages = [];
            this.isTemporaryConversation = true;
            
            // 滚动到底部
            this.$nextTick(() => {
                this.scrollToBottom();
            });
        },

        // 选择会话
        async selectConversation(conversation) {
            this.currentConversation = conversation;
            this.isTemporaryConversation = false; // 选择已有会话时，重置临时会话标志
            
            try {
                // 加载会话消息
                this.messages = await apiService.getMessages(conversation.id);
                
                // 为每条消息添加引用展开状态
                this.messages.forEach(message => {
                    if (message.references) {
                        message.referencesExpanded = false;
                        message.references.forEach(ref => {
                            ref.expanded = false;
                        });
                    }
                });
                
                // 滚动到底部
                this.$nextTick(() => {
                    this.scrollToBottom();
                });
            } catch (error) {
                console.error('加载消息失败:', error);
                this.messages = [];
                alert('加载消息失败: ' + error.message);
            }
        },

        // 删除会话
        async deleteConversation(conversation) {
            if (conversation.id === 1) {
                alert('默认会话不能删除');
                return;
            }

            if (!confirm('确定要删除这个会话吗？')) {
                return;
            }

            try {
                await apiService.deleteConversation(conversation.id);
                
                // 从列表中移除
                const index = this.conversations.findIndex(c => c.id === conversation.id);
                if (index > -1) {
                    this.conversations.splice(index, 1);
                }

                // 如果删除的是当前会话，清空当前会话
                if (this.currentConversation && this.currentConversation.id === conversation.id) {
                    this.currentConversation = null;
                    this.messages = [];
                }
            } catch (error) {
                console.error('删除会话失败:', error);
                alert('删除会话失败: ' + error.message);
            }
        },

        // 发送消息
        async sendMessage() {
            // 检查输入和状态
            if (!this.userInput.trim() || this.isGenerating || !this.currentConversation) {
                return;
            }

            const message = this.userInput.trim();
            this.userInput = '';

            try {
                // 如果是临时会话，先创建真实会话
                let conversationId = this.currentConversation.id;
                if (this.isTemporaryConversation) {
                    const newConversation = await apiService.createConversation('新的对话');
                    conversationId = newConversation.id;
                    this.currentConversation.id = conversationId;
                    this.isTemporaryConversation = false;
                    
                    // 添加到会话列表
                    this.conversations.unshift(newConversation);
                }

                // 添加用户消息到界面
                const userMessage = {
                    role: 'user',
                    content: message,
                    timestamp: new Date().toISOString()
                };
                this.messages.push(userMessage);

                // 创建助手消息占位符
                const assistantMessage = {
                    role: 'assistant',
                    content: '',
                    timestamp: new Date().toISOString(),
                    references: [],
                    referencesExpanded: false
                };
                this.messages.push(assistantMessage);

                // 滚动到底部
                this.$nextTick(() => {
                    this.scrollToBottom();
                });

                // 开始流式响应
                this.isGenerating = true;
                await this.streamResponse(conversationId, message, assistantMessage);
            } catch (error) {
                console.error('发送消息失败:', error);
                alert('发送消息失败: ' + error.message);
            } finally {
                this.isGenerating = false;
            }
        },

        // 流式响应处理
        async streamResponse(conversationId, userMessage, assistantMessage) {
            try {
                // 调用流式API
                await apiService.streamChat(conversationId, userMessage, (data) => {
                    if (data.code === 200) {
                        const chunkData = data.data;
                        
                        // 处理引用信息
                        if (chunkData.references) {
                            assistantMessage.references = chunkData.references;
                            // 默认折叠所有引用
                            assistantMessage.references.forEach(ref => {
                                ref.expanded = false;
                            });
                        } 
                        // 处理内容信息
                        else if (chunkData.content) {
                            assistantMessage.content += chunkData.content;
                            
                            // 滚动到底部
                            this.$nextTick(() => {
                                this.scrollToBottom();
                            });
                        }
                    }
                });
            } catch (error) {
                console.error('流式响应处理失败:', error);
                assistantMessage.content = '抱歉，回复生成失败: ' + error.message;
            }
        },

        // 切换所有引用的展开/折叠状态
        toggleReferences(message) {
            message.referencesExpanded = !message.referencesExpanded;

            // 如果展开，默认展开第一个引用
            if (message.referencesExpanded && message.references && message.references.length > 0) {
                // 可以选择是否默认展开第一个引用
                // message.references[0].expanded = true;
            }
        },

        // 切换单个引用的展开/折叠状态
        toggleReference(reference) {
            reference.expanded = !reference.expanded;
        },

        // 渲染Markdown内容
        renderMarkdown(content) {
            if (!content) return '';

            try {
                // 使用marked库渲染Markdown
                return marked.parse(content);
            } catch (error) {
                console.error('Markdown渲染失败:', error);
                // 简单的Markdown渲染（备用方案）
                return content
                    .replace(/### (.*?)(?=\n|$)/g, '<h3>$1</h3>')
                    .replace(/## (.*?)(?=\n|$)/g, '<h2>$1</h2>')
                    .replace(/# (.*?)(?=\n|$)/g, '<h1>$1</h1>')
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/`(.*?)`/g, '<code>$1</code>')
                    .replace(/\n/g, '<br>');
            }
        },

        // 格式化时间
        formatTime(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        },

        // 滚动到底部
        scrollToBottom() {
            const container = this.$refs.messagesContainer;
            if (container) {
                container.scrollTop = container.scrollHeight;
            }
        },

        // 退出登录
        async logout() {
            if (confirm('确定要退出登录吗？')) {
                // 清除认证信息
                localStorage.removeItem('token');
                // 跳转到登录页面
                window.location.href = './login.html';
            }
        }
    }
});

// 挂载应用
app.mount('#app');
