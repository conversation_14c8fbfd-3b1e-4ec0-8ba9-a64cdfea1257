/**
 * 前端配置文件
 * 管理生产环境和开发环境的不同配置
 */

// 检测是否为生产环境
const isProduction = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';

// 配置对象
const config = {
    // 环境标识
    environment: isProduction ? 'production' : 'development',
    
    // API配置
    api: {
        baseUrl: isProduction ? '' : '',  // 生产环境使用相对路径
        timeout: 30000,
        retryCount: 3
    },
    
    // UI配置
    ui: {
        animationDuration: 300,
        debounceDelay: 300,
        autoSaveDelay: 1000
    },
    
    // 功能开关
    features: {
        enableDebugMode: !isProduction,
        enablePerformanceMonitoring: isProduction,
        enableErrorReporting: isProduction
    },
    
    // 缓存配置
    cache: {
        tokenKey: 'token',
        userKey: 'currentUser',
        conversationsKey: 'conversations_cache',
        ttl: 3600000 // 1小时
    }
};

// 导出配置
window.AppConfig = config;

// 开发环境下输出配置信息
if (!isProduction) {
    console.log('App Config:', config);
}
