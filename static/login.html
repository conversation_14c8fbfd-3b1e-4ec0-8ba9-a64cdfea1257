<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>银通Wiki - 登录</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-gray-100">
    <div id="app" class="h-screen">
        <div class="flex items-center justify-center h-full bg-gradient-to-br from-blue-50 to-indigo-100">
            <div class="w-full max-w-md p-8 space-y-8 bg-white rounded-xl shadow-md">
                <div class="text-center">
                    <div class="mx-auto h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center">
                        <img src="images/logo.png" alt="Logo" class="h-12 w-12">
                    </div>
                    <h2 class="mt-6 text-3xl font-bold text-gray-900">银通Wiki</h2>
                    <p class="mt-2 text-sm text-gray-600">请登录您的账户</p>
                </div>
                <form class="mt-8 space-y-6" @submit.prevent="handleLogin">
                    <div class="space-y-4">
                        <div>
                            <label for="username" class="block text-sm font-medium text-gray-700">用户名</label>
                            <input id="username" name="username" type="text" required v-model="loginForm.username"
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700">密码</label>
                            <input id="password" name="password" type="password" required v-model="loginForm.password"
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>

                    <div v-if="loginError" class="text-red-500 text-sm text-center">
                        {{ loginError }}
                    </div>

                    <div>
                        <button type="submit" :disabled="isLoggingIn"
                            class="flex justify-center items-center w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
                            <span v-if="isLoggingIn" class="mr-2">
                                <i class="fas fa-spinner fa-spin"></i>
                            </span>
                            {{ isLoggingIn ? '登录中...' : '登录' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/utils.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/login.js"></script>
</body>
</html>