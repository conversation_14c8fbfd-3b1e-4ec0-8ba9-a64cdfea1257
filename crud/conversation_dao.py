import logging
from sqlalchemy.orm import Session, joinedload, selectinload
from sqlalchemy import func, and_, desc, asc
from typing import List, Optional, Tuple, Dict, Any
from datetime import datetime, timedelta

from models.conversation import Conversation
from models.message import Message
from utils.exceptions import DatabaseError, ConversationNotFoundError

logger = logging.getLogger(__name__)


class ConversationDao:
    """会话数据访问对象，提供优化的数据库查询"""

    @staticmethod
    def get_by_username(
        db: Session,
        username: str,
        page: int = 1,
        size: int = 20,
    ) -> Tuple[List[Conversation], int]:
        """
        分页获取用户的会话列表
        """
        try:
            query = db.query(Conversation).filter(Conversation.username == username)

            # 获取总数
            total = query.count()

            # 分页查询 - 置顶对话优先，然后按更新时间排序
            conversations = (
                query.order_by(desc(Conversation.sticky_flag), desc(Conversation.updated_at))
                .offset((page - 1) * size)
                .limit(size)
                .all()
            )

            logger.debug(f"Retrieved {len(conversations)} conversations for user {username}")
            return conversations, total

        except Exception as e:
            logger.error(f"Failed to get conversations for user {username}: {e}")
            raise DatabaseError(f"获取会话列表失败: {str(e)}")

    @staticmethod
    def get_by_id(
        db: Session,
        conversation_id: int,
        username: str = None,
        include_messages: bool = False
    ) -> Optional[Conversation]:
        """
        根据ID获取会话
        """
        try:
            query = db.query(Conversation).filter(Conversation.id == conversation_id)

            if username:
                query = query.filter(Conversation.username == username)

            # 如果需要消息，使用预加载
            if include_messages:
                query = query.options(
                    selectinload(Conversation.messages).options(
                        # 按时间戳排序消息
                        selectinload(Message).order_by(Message.timestamp)
                    )
                )

            conversation = query.first()

            if conversation:
                logger.debug(f"Retrieved conversation {conversation_id}")
            else:
                logger.warning(f"Conversation {conversation_id} not found")

            return conversation

        except Exception as e:
            logger.error(f"Failed to get conversation {conversation_id}: {e}")
            raise DatabaseError(f"获取会话失败: {str(e)}")

    @staticmethod
    def create(
        db: Session,
        conversation_id: int = None,
        username: str = None,
        title: str = "新的对话",
        auto_commit: bool = True
    ) -> Conversation:
        """
        创建新会话
        """
        try:
            if conversation_id:
                conversation = Conversation(
                    id=conversation_id,
                    username=username,
                    title=title,
                    sticky_flag=False,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
            else:
                conversation = Conversation(
                    username=username,
                    title=title,
                    sticky_flag=False,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )

            db.add(conversation)

            if auto_commit:
                db.commit()
                db.refresh(conversation)

            logger.info(f"Created conversation {conversation.id} for user {username}")
            return conversation

        except Exception as e:
            if auto_commit:
                db.rollback()
            logger.error(f"Failed to create conversation for user {username}: {e}")
            raise DatabaseError(f"创建会话失败: {str(e)}")

    @staticmethod
    def delete(
        db: Session,
        conversation: Conversation = None,
        conversation_id: int = None,
        username: str = None,
        auto_commit: bool = True
    ) -> bool:
        """
        删除会话及其所有消息
        """
        try:
            if not conversation and conversation_id:
                conversation = ConversationDao.get_by_id(db, conversation_id, username)

            if not conversation:
                raise ConversationNotFoundError(conversation_id or 0)

            # 先删除相关消息
            db.query(Message).filter(Message.conversation_id == conversation.id).delete()

            # 再删除会话
            db.delete(conversation)

            if auto_commit:
                db.commit()

            logger.info(f"Deleted conversation {conversation.id}")
            return True

        except ConversationNotFoundError:
            raise
        except Exception as e:
            if auto_commit:
                db.rollback()
            logger.error(f"Failed to delete conversation: {e}")
            raise DatabaseError(f"删除会话失败: {str(e)}")

    @staticmethod
    def update_sticky_status(
        db: Session,
        conversation_id: int,
        username: str,
        sticky_flag: bool,
        auto_commit: bool = True
    ) -> Optional[Conversation]:
        """
        更新会话置顶状态

        Args:
            db: 数据库会话
            conversation_id: 会话ID
            username: 用户名
            sticky_flag: 置顶状态
            auto_commit: 是否自动提交

        Returns:
            Optional[Conversation]: 更新后的会话对象
        """
        try:
            conversation = ConversationDao.get_by_id(db, conversation_id, username)

            if not conversation:
                raise ConversationNotFoundError(conversation_id)

            conversation.sticky_flag = sticky_flag
            conversation.updated_at = datetime.utcnow()

            if auto_commit:
                db.commit()
                db.refresh(conversation)

            logger.info(f"Updated sticky status for conversation {conversation_id} to {sticky_flag}")
            return conversation

        except ConversationNotFoundError:
            raise
        except Exception as e:
            if auto_commit:
                db.rollback()
            logger.error(f"Failed to update sticky status for conversation {conversation_id}: {e}")
            raise DatabaseError(f"更新置顶状态失败: {str(e)}")
