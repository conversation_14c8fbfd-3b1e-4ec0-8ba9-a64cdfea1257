"""FastAPI main application."""

import time
import json
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from starlette.responses import RedirectResponse
from starlette.staticfiles import StaticFiles

from config.settings import settings
from crud.database import create_tables, check_database_health
from api import auth_router, chat_router, conversations_router, messages_router
from utils.logging_config import get_logger, log_request, log_error
from utils.exception_handlers import register_exception_handlers
from services.cache_service import check_cache_health

# 设置日志
logger = get_logger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("Starting application...")
    try:
        # 创建数据库表
        create_tables()
        logger.info("Database tables created successfully")

        # 检查各种服务健康状态
        db_healthy = check_database_health()
        cache_healthy = await check_cache_health()

        logger.info(f"Service health check - DB: {db_healthy}, Cache: {cache_healthy}")

        if not db_healthy:
            logger.warning("Database health check failed")

        logger.info("Application startup completed")

    except Exception as e:
        logger.error(f"Application startup failed: {e}")
        raise

    yield

    # 关闭时执行
    logger.info("Shutting down application...")


# Create FastAPI application
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="基于FastAPI的RAG系统",
    docs_url="/docs" if not settings.is_production else None,
    redoc_url="/redoc" if not settings.is_production else None,
    lifespan=lifespan
)

# 注册异常处理器
register_exception_handlers(app)

# 添加安全中间件
if settings.is_production:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["localhost", "127.0.0.1", "*.yourdomain.com"]
    )

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.get_allowed_origins(),
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)


@app.middleware("http")
async def log_requests(request: Request, call_next):
    """请求日志中间件"""
    start_time = time.time()

    # 获取用户信息（如果存在）
    username = None
    if hasattr(request.state, 'username'):
        username = request.state.username

    response = await call_next(request)

    # 计算响应时间
    process_time = time.time() - start_time

    # 记录请求日志
    log_request(
        logger=logger,
        method=request.method,
        path=str(request.url.path),
        status_code=response.status_code,
        response_time=process_time,
        username=username
    )

    # 添加响应头
    response.headers["X-Process-Time"] = str(process_time)

    return response


# Include API routers
app.include_router(auth_router)
app.include_router(conversations_router)
app.include_router(messages_router)
app.include_router(chat_router)

app.mount("/static", StaticFiles(directory="static"), name="static")

# 访问根路径时跳转到 login.html
@app.get("/")
async def redirect_to_login():
    return RedirectResponse(url="/static/login.html")


@app.get("/health", summary="详细健康检查")
async def health_check():
    """Detailed health check endpoint."""
    try:
        # 检查数据库
        db_healthy = check_database_health()

        # 检查缓存
        cache_healthy = await check_cache_health()

        # 检查LLM服务（可选）
        # llm_healthy = await check_llm_health()

        health_status = {
            "status": "healthy" if all([db_healthy, cache_healthy]) else "unhealthy",
            "timestamp": time.time(),
            "services": {
                "database": "healthy" if db_healthy else "unhealthy",
                "cache": "healthy" if cache_healthy else "unhealthy",
                # "llm": "healthy" if llm_healthy else "unhealthy",
            },
            "version": settings.APP_VERSION,
            "environment": settings.ENVIRONMENT
        }

        status_code = 200 if health_status["status"] == "healthy" else 503
        return Response(
            content=json.dumps(health_status, ensure_ascii=False),
            status_code=status_code,
            media_type="application/json"
        )

    except Exception as e:
        log_error(logger, e, {"endpoint": "/health"})
        return Response(
            content=json.dumps({
                "code": 1005,
                "message": "内部服务器错误",
                "data": None
            }, ensure_ascii=False),
            status_code=500,
            media_type="application/json"
        )


if __name__ == "__main__":
    import uvicorn

    uvicorn.run("main:app", host="0.0.0.0", port=8001, reload=settings.DEBUG)