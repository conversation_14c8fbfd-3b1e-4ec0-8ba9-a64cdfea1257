#!/usr/bin/env python3
"""
完整的前端功能测试脚本
"""

import requests
import json
import time

BASE_URL = "http://localhost:8001"

def test_complete_workflow():
    """测试完整的工作流程"""
    print("=== 完整前端功能测试 ===")
    
    # 1. 登录
    print("\n1. 测试登录...")
    login_data = {
        "username": "testuser",
        "password": "testpass"
    }
    
    response = requests.post(f"{BASE_URL}/api/login", json=login_data)
    if response.status_code != 200:
        print(f"❌ 登录失败: {response.status_code}")
        return
    
    login_result = response.json()
    token = login_result['data']['access_token']
    print(f"✅ 登录成功，token: {token[:20]}...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 2. 获取初始对话列表
    print("\n2. 获取初始对话列表...")
    response = requests.get(f"{BASE_URL}/api/conversations", headers=headers)
    if response.status_code != 200:
        print(f"❌ 获取对话列表失败: {response.status_code}")
        return
    
    conversations_result = response.json()
    conversations = conversations_result['data']
    print(f"✅ 获取到 {len(conversations)} 个对话")
    
    for conv in conversations:
        print(f"   - ID: {conv['id']}, 标题: {conv['title']}, 置顶: {conv.get('sticky_flag', False)}")
    
    # 3. 创建新对话用于测试
    print("\n3. 创建测试对话...")
    create_data = {"title": "前端测试对话"}
    response = requests.post(f"{BASE_URL}/api/conversations/new", json=create_data, headers=headers)
    if response.status_code != 200:
        print(f"❌ 创建对话失败: {response.status_code}")
        return
    
    new_conv = response.json()['data']
    test_conv_id = new_conv['id']
    print(f"✅ 创建测试对话成功，ID: {test_conv_id}")
    
    # 4. 测试置顶功能
    print("\n4. 测试置顶功能...")
    
    # 4.1 设置置顶
    sticky_data = {"sticky_flag": True}
    response = requests.put(f"{BASE_URL}/api/conversations/{test_conv_id}/sticky", 
                          json=sticky_data, headers=headers)
    if response.status_code != 200:
        print(f"❌ 设置置顶失败: {response.status_code} - {response.text}")
        return
    
    sticky_result = response.json()
    print(f"✅ 设置置顶成功")
    print(f"   API返回: {json.dumps(sticky_result['data'], indent=2, ensure_ascii=False)}")
    
    # 4.2 验证置顶状态
    print("\n5. 验证置顶状态...")
    response = requests.get(f"{BASE_URL}/api/conversations", headers=headers)
    if response.status_code == 200:
        conversations = response.json()['data']
        test_conv = next((c for c in conversations if c['id'] == test_conv_id), None)
        if test_conv:
            print(f"✅ 对话置顶状态: {test_conv.get('sticky_flag', False)}")
            if test_conv.get('sticky_flag', False):
                print("   ✅ 置顶功能正常工作")
            else:
                print("   ❌ 置顶状态未正确更新")
        else:
            print("❌ 未找到测试对话")
    
    # 4.3 取消置顶
    print("\n6. 测试取消置顶...")
    sticky_data = {"sticky_flag": False}
    response = requests.put(f"{BASE_URL}/api/conversations/{test_conv_id}/sticky", 
                          json=sticky_data, headers=headers)
    if response.status_code == 200:
        print("✅ 取消置顶成功")
        
        # 验证取消置顶状态
        response = requests.get(f"{BASE_URL}/api/conversations", headers=headers)
        if response.status_code == 200:
            conversations = response.json()['data']
            test_conv = next((c for c in conversations if c['id'] == test_conv_id), None)
            if test_conv:
                print(f"✅ 取消置顶后状态: {test_conv.get('sticky_flag', False)}")
                if not test_conv.get('sticky_flag', False):
                    print("   ✅ 取消置顶功能正常工作")
                else:
                    print("   ❌ 取消置顶状态未正确更新")
    else:
        print(f"❌ 取消置顶失败: {response.status_code}")
    
    # 7. 测试对话排序
    print("\n7. 测试对话排序...")
    
    # 创建另一个对话并置顶
    create_data = {"title": "置顶测试对话2"}
    response = requests.post(f"{BASE_URL}/api/conversations/new", json=create_data, headers=headers)
    if response.status_code == 200:
        conv2_id = response.json()['data']['id']
        
        # 置顶第二个对话
        sticky_data = {"sticky_flag": True}
        response = requests.put(f"{BASE_URL}/api/conversations/{conv2_id}/sticky", 
                              json=sticky_data, headers=headers)
        
        if response.status_code == 200:
            print("✅ 创建并置顶第二个测试对话")
            
            # 获取对话列表检查排序
            response = requests.get(f"{BASE_URL}/api/conversations", headers=headers)
            if response.status_code == 200:
                conversations = response.json()['data']
                print("✅ 最终对话列表排序:")
                for i, conv in enumerate(conversations):
                    sticky_status = "📌" if conv.get('sticky_flag', False) else "  "
                    print(f"   {i+1}. {sticky_status} {conv['title']} (ID: {conv['id']})")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_complete_workflow()
