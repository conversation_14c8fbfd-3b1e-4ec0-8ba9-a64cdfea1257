#!/usr/bin/env python3
"""测试流式接口"""

import requests
import json

def test_stream_api():
    """测试流式API"""
    url = "http://localhost:8001/api/chat/stream"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ3YW5nemhpeGluIiwidHlwZSI6ImFjY2Vzc190b2tlbiIsImlhdCI6MTc1Mzc1Njg4MSwiZXhwIjoxNzUzNzYwNDgxfQ.sMJJkGuXkfOnkkFCKf1r3o9SJjmA8a99-UARkwMNU00"
    }
    data = {
        "conversation_id": 1753608474944,
        "message": "你好，请简单介绍一下自己",
        "collection_name": "FinancialResearchOffice",
        "input": {}
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, stream=True)
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print("Stream Response:")
        print("-" * 50)
        
        for line in response.iter_lines(decode_unicode=True):
            if line:
                print(f"Raw line: {repr(line)}")
                if line.startswith('data: '):
                    data_part = line[6:]  # Remove 'data: ' prefix
                    if data_part == '[DONE]':
                        print("Stream ended")
                        break
                    else:
                        try:
                            parsed = json.loads(data_part)
                            print(f"Parsed data: {parsed}")
                        except json.JSONDecodeError as e:
                            print(f"JSON decode error: {e}")
                            print(f"Raw data: {data_part}")
                
    except requests.exceptions.RequestException as e:
        print(f"Request error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")

if __name__ == "__main__":
    test_stream_api()
