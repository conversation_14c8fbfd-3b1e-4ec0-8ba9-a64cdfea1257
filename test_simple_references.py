#!/usr/bin/env python3
"""
简单的引用功能测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from crud.database import get_database_session
from crud.conversation_dao import ConversationDao
from crud.message_dao import MessageDao
from services.message_service import MessageService
from schemas.message import ChatRequest
import logging
import json

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_simple_references():
    """简单的引用功能测试"""
    try:
        # 获取数据库会话
        db = next(get_database_session())
        
        # 创建服务实例
        message_service = MessageService()
        
        # 手动创建测试对话
        test_username = "test_user"
        conversation = ConversationDao.create(db, username=test_username, title="测试引用功能")
        logger.info(f"创建测试对话: {conversation.id}")
        
        # 手动创建用户消息
        user_message = MessageDao.create(
            db, conversation.id, "user", "什么是金融？"
        )
        logger.info(f"创建用户消息: {user_message.id}")
        
        # 创建聊天请求
        chat_data = ChatRequest(
            conversation_id=conversation.id,
            message="什么是金融？",
            collection_name="FinancialResearchOffice",
            input={}
        )
        
        # 生成AI回复和引用
        content_generator, references = message_service.generate_chat_response_with_references(chat_data)
        
        # 收集完整的AI回复内容
        ai_content = ""
        for chunk in content_generator:
            ai_content += chunk
        
        logger.info(f"AI回复内容长度: {len(ai_content)}")
        logger.info(f"引用数量: {len(references)}")
        
        # 手动保存AI消息（包含引用）
        ai_message = MessageDao.create(
            db, conversation.id, "assistant", ai_content, user_message.id, references
        )
        
        logger.info(f"创建AI消息: {ai_message.id}")
        
        # 验证数据库中的引用内容
        db.refresh(ai_message)
        saved_references = ai_message.references
        
        if saved_references:
            logger.info(f"数据库中保存的引用数量: {len(saved_references)}")
            for i, ref in enumerate(saved_references, 1):
                logger.info(f"引用 {i}: {ref.get('title', 'N/A')} (得分: {ref.get('score', 0.0):.3f})")
        else:
            logger.warning("数据库中没有保存引用内容")
        
        # 测试消息检索
        messages, total = MessageDao.get_by_conversation_id(db, conversation.id)
        logger.info(f"检索到 {len(messages)} 条消息")
        
        for msg in messages:
            logger.info(f"消息 {msg.id}: {msg.role} - 内容长度: {len(msg.content)}")
            if msg.references:
                logger.info(f"  引用数量: {len(msg.references)}")
                # 打印第一个引用的详细信息
                if len(msg.references) > 0:
                    first_ref = msg.references[0]
                    logger.info(f"  第一个引用: {first_ref.get('title', 'N/A')}")
        
        # 清理测试数据
        db.delete(ai_message)
        db.delete(user_message)
        db.delete(conversation)
        db.commit()
        
        logger.info("测试数据已清理")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    logger.info("开始简单的引用功能测试...")
    success = test_simple_references()
    if success:
        logger.info("测试成功完成!")
    else:
        logger.error("测试失败!")
        sys.exit(1)
