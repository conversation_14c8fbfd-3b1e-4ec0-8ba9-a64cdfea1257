#!/usr/bin/env python3
"""
数据库迁移脚本：添加references字段到message表
"""

import sqlite3
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_database():
    """添加references字段到message表"""
    try:
        # 连接数据库
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        # 检查是否已经存在references字段
        cursor.execute("PRAGMA table_info(message)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'references' in column_names:
            logger.info("references字段已存在，无需迁移")
            return True
        
        logger.info("开始添加references字段...")
        
        # 添加references字段（使用引号包围保留字）
        cursor.execute('ALTER TABLE message ADD COLUMN "references" TEXT')
        
        # 提交更改
        conn.commit()
        
        logger.info("references字段添加成功")
        
        # 验证字段是否添加成功
        cursor.execute("PRAGMA table_info(message)")
        columns = cursor.fetchall()
        logger.info("当前message表字段:")
        for col in columns:
            logger.info(f"  {col[1]} ({col[2]})")
        
        return True
        
    except Exception as e:
        logger.error(f"数据库迁移失败: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    logger.info("开始数据库迁移...")
    success = migrate_database()
    if success:
        logger.info("数据库迁移成功完成!")
    else:
        logger.error("数据库迁移失败!")
        exit(1)
