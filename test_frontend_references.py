#!/usr/bin/env python3
"""
测试前端引用功能的脚本
"""

import requests
import json
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_frontend_references():
    """测试前端引用功能"""
    try:
        # 1. 登录
        login_response = requests.post('http://localhost:8001/api/login', json={
            'username': 'test_user',
            'password': 'test_password'
        })
        
        if login_response.status_code != 200:
            logger.error(f"登录失败: {login_response.status_code}")
            return False
        
        login_data = login_response.json()
        token = login_data['data']['access_token']
        logger.info("✅ 登录成功")
        
        # 2. 创建对话
        conv_response = requests.post('http://localhost:8001/api/conversations/new', 
            json={'title': '测试前端引用功能'},
            headers={'Authorization': f'Bearer {token}'}
        )
        
        conv_data = conv_response.json()
        conversation_id = conv_data['data']['id']
        logger.info(f"✅ 创建对话成功: {conversation_id}")
        
        # 3. 发送消息并检查引用
        logger.info("📤 发送消息: '什么是金融？'")
        
        stream_response = requests.post('http://localhost:8001/api/chat/stream',
            json={
                'conversation_id': conversation_id,
                'message': '什么是金融？',
                'collection_name': 'FinancialResearchOffice',
                'input': {}
            },
            headers={'Authorization': f'Bearer {token}'},
            stream=True
        )
        
        if stream_response.status_code != 200:
            logger.error(f"流式请求失败: {stream_response.status_code}")
            return False
        
        # 4. 解析SSE流并验证引用
        references_received = False
        content_received = False
        references_data = None
        content_chunks = []
        
        logger.info("📡 开始接收SSE流...")
        
        for line in stream_response.iter_lines(decode_unicode=True):
            if line.startswith('data: '):
                data_str = line[6:].strip()
                
                if data_str == '[DONE]':
                    break
                
                try:
                    data = json.loads(data_str)
                    
                    if data.get('code') == 200 and data.get('data'):
                        data_content = data['data']
                        
                        if data_content.get('type') == 'references':
                            references_data = data_content.get('references', [])
                            references_received = True
                            logger.info(f"📚 接收到引用内容: {len(references_data)} 个引用")
                            
                        elif data_content.get('type') == 'content':
                            content = data_content.get('content', '')
                            content_chunks.append(content)
                            content_received = True
                
                except json.JSONDecodeError:
                    continue
        
        full_content = ''.join(content_chunks)
        logger.info(f"📝 接收到完整内容: {len(full_content)} 字符")
        
        # 5. 验证结果
        if not references_received:
            logger.error("❌ 未接收到引用内容")
            return False
        
        if not content_received:
            logger.error("❌ 未接收到AI回复内容")
            return False
        
        # 6. 检查引用内容格式
        if not references_data or len(references_data) == 0:
            logger.error("❌ 引用内容为空")
            return False
        
        logger.info("🔍 验证引用内容格式:")
        for i, ref in enumerate(references_data, 1):
            required_fields = ['id', 'title', 'content', 'score']
            missing_fields = [field for field in required_fields if field not in ref]
            
            if missing_fields:
                logger.error(f"❌ 引用 {i} 缺少字段: {missing_fields}")
                return False
            
            logger.info(f"  ✅ 引用 {i}: {ref['title']} (得分: {ref['score']:.3f})")
        
        # 7. 获取消息历史验证数据库保存
        time.sleep(1)  # 等待数据库保存
        
        messages_response = requests.get(f'http://localhost:8001/api/messages?conversation_id={conversation_id}',
            headers={'Authorization': f'Bearer {token}'}
        )
        
        if messages_response.status_code != 200:
            logger.error(f"获取消息历史失败: {messages_response.status_code}")
            return False
        
        messages_data = messages_response.json()
        messages = messages_data['data']
        
        # 查找AI消息
        ai_message = None
        for msg in messages:
            if msg['role'] == 'assistant':
                ai_message = msg
                break
        
        if not ai_message:
            logger.error("❌ 未找到AI消息")
            return False
        
        if not ai_message.get('references'):
            logger.error("❌ 数据库中AI消息没有引用内容")
            return False
        
        logger.info(f"✅ 数据库中保存了 {len(ai_message['references'])} 个引用")
        
        logger.info("🎉 前端引用功能测试完全成功!")
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    logger.info("🚀 开始测试前端引用功能...")
    success = test_frontend_references()
    if success:
        logger.info("✅ 测试成功完成!")
    else:
        logger.error("❌ 测试失败!")
        exit(1)
