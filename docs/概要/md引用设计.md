# MD引用设计文档

## 1. 概述

本文档旨在详细说明如何在AI回复中实现引用内容的显示功能。根据需求，AI回复需要包含引用的内容，这些引用内容在前端将以Markdown格式显示，并支持折叠和展开功能。折叠时显示为一行，展开时显示完整内容。

当前/chat/stream接口以流式方式返回数据，每个数据片段都遵循标准格式：
```json
{
  "code": 200,
  "message": "success", 
  "data": {"content": "内容片段"}
}
```

本文档将基于该接口特性设计引用内容的显示方案。

## 2. 功能需求

### 2.1 核心功能
1. AI回复中包含引用内容
2. 引用内容以Markdown格式显示
3. 引用内容支持折叠/展开交互
4. 折叠状态下显示为单行
5. 展开状态下显示完整内容

### 2.2 显示要求
1. AI回复内容同样以Markdown格式显示
2. 引用内容与AI回复内容分离显示
3. 引用内容区域有明确的视觉标识

## 3. 设计方案

### 3.1 后端数据结构设计

由于/chat/stream接口是流式输出，我们需要在流开始时发送引用信息，然后在流式传输过程中发送AI回复内容。

#### 3.1.1 初始数据包（包含引用信息）
在流开始时发送一个包含引用信息的数据包：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "references": [
      {
        "id": 1,
        "title": "人工智能简介",
        "content": "人工智能（Artificial Intelligence），英文缩写为AI。它是研究、开发用于模拟、延伸和扩展人的智能的理论、方法、技术及应用系统的一门新的技术科学。",
      },
      {
        "id": 2,
        "title": "机器学习基础",
        "content": "机器学习是人工智能的一个子集，它使计算机能够从数据中学习并做出决策或预测。",
      }
    ]
  }
}
```

#### 3.1.2 流式内容数据包
后续数据包继续传输AI回复内容：

```json
{
  "code": 200,
  "message": "success", 
  "data": {"content": "内容片段"}
}
```

#### 3.1.3 结束标记
流结束时发送结束标记：

```json
data: [DONE]
```

### 3.2 前端显示设计

#### 3.2.1 整体布局
```
+-------------------------------------------------------------+
| AI回复内容（Markdown格式）                                  |
|                                                             |
| 这里是AI的回复内容，支持Markdown格式显示...                 |
|                                                             |
+-------------------------------------------------------------+
| [参考资料] [▼]                                              |
+-------------------------------------------------------------+
| 引用内容1（折叠状态下显示单行）                             |
+-------------------------------------------------------------+
| 引用内容2（折叠状态下显示单行）                             |
+-------------------------------------------------------------+
```

#### 3.2.2 折叠状态
- 默认状态下，引用内容区域处于折叠状态
- 每个引用内容仅显示标题或内容的前N个字符，以单行形式展示
- 右上角显示展开/折叠图标（▼/▲）

#### 3.2.3 展开状态
- 用户点击参考资料区域或展开按钮后，引用内容完全展开
- 显示引用内容的完整信息，包括标题和内容
- 支持Markdown格式渲染
- 右上角显示折叠按钮（▲）

### 3.3 交互设计

#### 3.3.1 展开/折叠操作
1. 点击"参考资料"标题区域可切换折叠/展开状态
2. 点击每个引用条目前的展开/折叠图标可单独控制该条目
3. 提供"展开全部"/"折叠全部"按钮

#### 3.3.2 引用内容操作
1. 点击引用标题可跳转到原始链接（如果有URL）
2. 支持复制引用内容
3. 提供引用内容的分享功能

#### 3.4.2 图标设计
- 折叠状态：▼ (fa-chevron-down)
- 展开状态：▲ (fa-chevron-up)
- 链接图标：↗ (fa-external-link-alt)


## 6. 注意事项

1. 引用内容可能较长，需要考虑性能优化
2. 需要处理Markdown渲染的安全性问题
3. 要确保在不同屏幕尺寸下的显示效果
5. 引用内容的URL需要进行安全检查
6. 需要确保流式传输过程中引用信息和内容的正确关联
7. 需要考虑网络中断等异常情况下的处理