# 前端API优化使用指南

## 概述

本次优化统一了前端对后端 `{code, message, data}` 格式响应的处理，创建了可复用的API服务组件，大大减少了重复代码并提高了代码的可维护性。

## 核心组件

### 1. ApiUtils (utils.js)

提供底层的API调用工具，包含：

- **标准化响应处理**：自动处理 `{code, message, data}` 格式
- **错误处理**：统一的错误处理和用户提示
- **重试机制**：带指数退避的自动重试
- **流式响应处理**：统一的SSE流式数据处理

#### 基本用法

```javascript
// GET请求
const result = await ApiUtils.get('/api/conversations');

// POST请求
const result = await ApiUtils.post('/api/login', {
    username: 'user',
    password: 'pass'
});

// 带认证的请求
const authApi = ApiUtils.withAuth(token);
const result = await authApi.get('/api/conversations');

// 流式请求
await authApi.stream('/api/chat/stream', data, {
    onData: (content) => console.log(content),
    onError: (error) => console.error(error),
    onComplete: () => console.log('完成')
});
```

### 2. ApiService (api-service.js)

高级API服务类，封装了所有业务API调用：

#### 认证相关

```javascript
// 登录
const result = await apiService.login(username, password);
if (result.success) {
    console.log('登录成功', result.user);
} else {
    console.error('登录失败', result.message);
}

// 登出
await apiService.logout();
```

#### 对话管理

```javascript
// 获取对话列表
const result = await apiService.getConversations();
if (result.success) {
    console.log('对话列表', result.conversations);
}

// 创建新对话
const result = await apiService.createConversation('对话标题');
if (result.success) {
    console.log('新对话', result.conversation);
}

// 删除对话
const result = await apiService.deleteConversation(conversationId);
```

#### 消息处理

```javascript
// 获取消息
const result = await apiService.getMessages(conversationId);
if (result.success) {
    console.log('消息列表', result.messages);
}

// 发送流式消息
await apiService.sendStreamMessage(conversationId, message, {
    onStart: () => console.log('开始发送'),
    onData: (content) => console.log('接收到:', content),
    onError: (error) => console.error('错误:', error),
    onComplete: () => console.log('发送完成')
});
```

### 3. ErrorHandler (utils.js)

统一的错误处理工具：

```javascript
// 显示错误提示
ErrorHandler.showErrorToast('操作失败');

// 显示成功提示
ErrorHandler.showSuccessToast('操作成功');

// API错误处理
ErrorHandler.handleApiError(error, '登录');
```

## 优化效果

### 代码减少

- **登录逻辑**：从 40+ 行减少到 15 行
- **对话加载**：从 45+ 行减少到 20 行
- **消息发送**：从 100+ 行减少到 25 行
- **流式处理**：从重复的 80+ 行代码变成统一的回调处理

### 错误处理统一

- 所有API调用都有统一的错误处理
- 自动显示用户友好的错误提示
- 统一的重试机制

### 类型安全

- 统一的响应格式处理
- 减少了手动解析JSON的错误
- 标准化的成功/失败判断

## 使用示例

### 替换前的代码

```javascript
// 旧的登录代码
try {
    const response = await fetch('/api/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password })
    });
    
    const data = await response.json();
    
    if (data.code === 200) {
        localStorage.setItem('token', data.data.access_token);
        localStorage.setItem('currentUser', data.data.user.username);
        // 成功处理
    } else {
        // 错误处理
    }
} catch (error) {
    // 异常处理
}
```

### 替换后的代码

```javascript
// 新的登录代码
const result = await apiService.login(username, password);
if (result.success) {
    localStorage.setItem('currentUser', result.user.username);
    // 成功处理
} else {
    // 错误已自动处理和显示
}
```

## 迁移指南

### 1. 引入新的工具文件

在HTML文件中添加：

```html
<script src="js/utils.js"></script>
<script src="js/api-service.js"></script>
```

### 2. 替换API调用

将现有的fetch调用替换为apiService调用：

```javascript
// 旧代码
const response = await fetch('/api/conversations', {
    headers: { 'Authorization': `Bearer ${token}` }
});
const data = await response.json();
if (data.code === 200) {
    // 处理数据
}

// 新代码
const result = await apiService.getConversations();
if (result.success) {
    // 处理数据
}
```

### 3. 流式响应处理

```javascript
// 旧代码：复杂的流式处理逻辑（80+行）

// 新代码：简洁的回调处理
await apiService.sendStreamMessage(conversationId, message, {
    onData: (content) => updateUI(content),
    onError: (error) => showError(error),
    onComplete: () => finishProcessing()
});
```

## 最佳实践

1. **统一使用apiService**：所有API调用都通过apiService进行
2. **错误处理**：依赖自动错误处理，减少手动错误处理代码
3. **流式处理**：使用回调模式处理流式响应
4. **认证管理**：使用apiService的认证状态管理
5. **代码复用**：新功能优先考虑扩展apiService而不是重写

## 扩展指南

### 添加新的API

1. 在ApiService类中添加新方法
2. 使用统一的错误处理模式
3. 返回标准化的结果格式

```javascript
async newApiMethod(param) {
    try {
        const result = await this.getAuthApi().post('/api/new-endpoint', param);
        if (result.success) {
            return { success: true, data: result.data };
        } else {
            return { success: false, message: result.message };
        }
    } catch (error) {
        return { success: false, message: error.message };
    }
}
```

这样的优化使前端代码更加简洁、可维护，并且减少了重复代码，提高了开发效率。
