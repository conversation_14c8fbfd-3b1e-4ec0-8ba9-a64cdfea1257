# AI回复引用功能设计文档

## 1. 概述

本文档详细描述了AI聊天系统中引用内容显示功能的设计与实现方案。根据需求，系统需要实现以下功能：

1. AI回复需要返回引用的内容
2. 引用内容前端用markdown格式显示，可以折叠和展开，折叠时为一行
3. AI回复内容同样以markdown格式显示

## 2. 功能需求

### 2.1 核心功能
1. AI回复中包含引用内容
2. 引用内容以Markdown格式显示
3. 引用内容支持折叠/展开交互
4. 折叠状态下显示为单行
5. 展开状态下显示完整内容

### 2.2 显示要求
1. AI回复内容同样以Markdown格式显示
2. 引用内容与AI回复内容分离显示
3. 引用内容区域有明确的视觉标识

## 3. 设计方案

### 3.1 后端数据结构设计

#### 3.1.1 消息数据模型扩展

在现有的消息模型中添加引用内容字段：

```python
# models/message.py
class Message(Base):
    references = Column(JSON, nullable=True)  # 存储引用内容的JSON数组
```

#### 3.1.2 引用内容数据结构

引用内容将采用以下JSON格式：

```json
{
  "content": "根据参考资料，人工智能是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。",
  "references": [
    {
      "id": 1,
      "title": "人工智能简介",
      "content": "人工智能（Artificial Intelligence），英文缩写为AI。它是研究、开发用于模拟、延伸和扩展人的智能的理论、方法、技术及应用系统的一门新的技术科学。",
      "source": "https://example.com/ai-intro",
      "score": 0.95
    },
    {
      "id": 2,
      "title": "机器学习基础",
      "content": "机器学习是人工智能的一个子集，它使计算机能够从数据中学习并做出决策或预测。",
      "source": "https://example.com/ml-basics",
      "score": 0.87
    }
  ]
}
```

### 3.2 前端显示设计

#### 3.2.1 整体布局

```
+-------------------------------------------------------------+
| AI回复内容（Markdown格式）                                  |
|                                                             |
| 这里是AI的回复内容，支持Markdown格式显示...                 |
|                                                             |
+-------------------------------------------------------------+
| [参考资料] [▼]                                              |
+-------------------------------------------------------------+
| 引用内容1（折叠状态下显示单行）                             |
+-------------------------------------------------------------+
| 引用内容2（折叠状态下显示单行）                             |
+-------------------------------------------------------------+
```

#### 3.2.2 折叠状态
- 默认状态下，引用内容区域处于折叠状态
- 每个引用内容仅显示标题和内容的前N个字符，以单行形式展示
- 右上角显示展开/折叠图标（▼/▲）

#### 3.2.3 展开状态
- 用户点击参考资料区域或展开按钮后，引用内容完全展开
- 显示引用内容的完整信息，包括标题和内容
- 支持Markdown格式渲染
- 右上角显示折叠按钮（▲）

### 3.3 交互设计

#### 3.3.1 展开/折叠操作
1. 点击"参考资料"标题区域可切换折叠/展开状态
2. 点击每个引用条目前的展开/折叠图标可单独控制该条目
3. 提供"展开全部"/"折叠全部"按钮

#### 3.3.2 引用内容操作
1. 点击引用标题可跳转到原始链接（如果有URL）
2. 支持复制引用内容
3. 提供引用内容的分享功能

## 4. 实现方案

### 4.1 后端实现

#### 4.1.1 数据模型更新

更新消息模型以支持存储引用内容：

```python
# models/message.py
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
from .base import Base

class Message(Base):
    __tablename__ = "messages"
    
    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=False)
    role = Column(String, nullable=False)  # 'user' 或 'assistant'
    content = Column(Text, nullable=False)
    references = Column(JSON, nullable=True)  # 新增：存储引用内容
    created_at = Column(DateTime, default=datetime.utcnow)
    
    conversation = relationship("Conversation", back_populates="messages")
```

#### 4.1.2 API接口更新

在获取消息的接口中返回引用内容：

```python
# api/messages.py
@router.get("/conversations/{conversation_id}/messages", response_model=List[MessageResponse])
async def get_messages(conversation_id: int, db: Session = Depends(get_db)):
    """获取对话中的所有消息，包括引用内容"""
    messages = MessageDao.get_messages_by_conversation_id(db, conversation_id)
    return messages
```

### 4.2 前端实现

#### 4.2.1 HTML模板更新

更新聊天界面中消息显示部分，支持引用内容的展示：

```html
<!-- static/chat.html -->
<div v-for="(message, index) in messages" :key="index" class="mb-4">
    <div :class="[
        'max-w-3/4 px-4 py-3 rounded-2xl shadow-sm',
        message.role === 'user'
            ? 'ml-auto bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-br-none'
            : 'mr-auto bg-gray-100 text-gray-800 rounded-bl-none'
    ]" style="width: fit-content;">

        <!-- AI回复内容 -->
        <div v-if="message.role === 'assistant'" class="space-y-3">
            <!-- AI回复文本 -->
            <div class="whitespace-pre-wrap markdown-content"
                 v-html="renderMarkdown(message.content)"></div>

            <!-- 引用内容区域 -->
            <div v-if="message.references && message.references.length > 0"
                 class="reference-container">

                <!-- 引用区域标题 -->
                <div class="reference-header" @click="toggleReferences(message)">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">
                            <i class="fas fa-book-open mr-2"></i>
                            参考资料 ({{ message.references.length }})
                        </span>
                        <i :class="[
                            'fas transition-transform duration-200',
                            message.referencesExpanded ? 'fa-chevron-up' : 'fa-chevron-down'
                        ]"></i>
                    </div>
                </div>

                <!-- 引用内容列表 -->
                <div v-show="message.referencesExpanded" class="reference-content">
                    <div v-for="(reference, refIndex) in message.references"
                         :key="reference.id"
                         class="reference-item">

                        <!-- 引用项标题 -->
                        <div class="reference-item-header" @click="toggleReference(reference)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <span class="reference-number">{{ refIndex + 1 }}</span>
                                    <span class="reference-title">{{ reference.title }}</span>
                                    <span class="reference-score">{{ (reference.score * 100).toFixed(1) }}%</span>
                                </div>
                                <i :class="[
                                    'fas transition-transform duration-200',
                                    reference.expanded ? 'fa-chevron-up' : 'fa-chevron-down'
                                ]"></i>
                            </div>
                        </div>

                        <!-- 引用项内容 -->
                        <div v-show="reference.expanded" class="reference-item-content">
                            <div class="reference-text markdown-content"
                                 v-html="renderMarkdown(reference.content)"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户消息内容 -->
        <div v-else class="whitespace-pre-wrap">{{ message.content }}</div>

        <!-- 时间戳 -->
        <div class="text-xs opacity-70 mt-2">
            {{ formatTime(message.timestamp) }}
        </div>
    </div>
</div>
```

#### 4.2.2 CSS样式设计

```css
/* static/css/styles.css */

/* 引用容器样式 */
.reference-container {
    @apply mt-3 border border-gray-200 rounded-lg bg-gray-50;
}

.reference-header {
    @apply px-3 py-2 cursor-pointer hover:bg-gray-100 transition-colors duration-200;
    border-bottom: 1px solid #e5e7eb;
}

.reference-content {
    @apply p-3 space-y-2;
}

/* 引用项样式 */
.reference-item {
    @apply border border-gray-200 rounded-md bg-white;
}

.reference-item-header {
    @apply px-3 py-2 cursor-pointer hover:bg-gray-50 transition-colors duration-200;
}

.reference-item-content {
    @apply px-3 pb-3;
}

/* 引用元素样式 */
.reference-number {
    @apply inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-blue-500 rounded-full;
}

.reference-title {
    @apply font-medium text-gray-800 truncate;
    max-width: 200px;
}

.reference-score {
    @apply text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full;
}

.reference-text {
    @apply text-sm text-gray-700 leading-relaxed;
}

/* Markdown内容样式 */
.markdown-content h1 {
    @apply text-lg font-bold mb-2;
}

.markdown-content h2 {
    @apply text-base font-bold mb-2;
}

.markdown-content h3 {
    @apply text-sm font-bold mb-1;
}

.markdown-content p {
    @apply mb-2;
}

.markdown-content code {
    @apply px-1 py-0.5 bg-gray-200 rounded text-sm font-mono;
}

.markdown-content pre {
    @apply p-3 bg-gray-100 rounded-md overflow-x-auto;
}

.markdown-content strong {
    @apply font-bold;
}

.markdown-content em {
    @apply italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .reference-title {
        max-width: 150px;
    }
}
```

#### 4.2.3 JavaScript功能实现

```javascript
// static/js/chat.js

// 切换所有引用的展开/折叠状态
const toggleReferences = (message) => {
    message.referencesExpanded = !message.referencesExpanded;

    // 如果展开，默认展开第一个引用
    if (message.referencesExpanded && message.references.length > 0) {
        message.references[0].expanded = true;
    }
};

// 切换单个引用的展开/折叠状态
const toggleReference = (reference) => {
    reference.expanded = !reference.expanded;
};

// 渲染Markdown内容
const renderMarkdown = (content) => {
    if (!content) return '';

    // 使用marked库渲染Markdown
    if (typeof marked !== 'undefined') {
        return marked.parse(content);
    }

    // 简单的Markdown渲染（如果没有marked库）
    return content
        .replace(/### (.*)/g, '<h3>$1</h3>')
        .replace(/## (.*)/g, '<h2>$1</h2>')
        .replace(/# (.*)/g, '<h1>$1</h1>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/`(.*?)`/g, '<code>$1</code>')
        .replace(/\n/g, '<br>');
};
```

## 5. 测试方案

### 5.1 功能测试
1. 验证AI回复内容正确显示
2. 验证引用内容正确显示
3. 验证折叠/展开功能正常
4. 验证Markdown渲染正确
5. 验证URL跳转功能正常

### 5.2 兼容性测试
1. 测试不同浏览器的兼容性
2. 测试移动端显示效果
3. 测试无引用内容时的显示
4. 测试大量引用内容时的性能

### 5.3 性能测试
1. 测试引用内容渲染性能
2. 测试折叠/展开操作的响应速度
3. 测试大数据量下的内存占用

## 6. 部署方案

### 6.1 前端部署
1. 更新HTML模板文件
2. 添加CSS样式
3. 更新JavaScript功能代码

### 6.2 后端部署
1. 更新数据库模型
2. 部署数据库迁移脚本
3. 更新API接口

## 7. 注意事项

1. 引用内容可能较长，需要考虑性能优化
2. 需要处理Markdown渲染的安全性问题
3. 要确保在不同屏幕尺寸下的显示效果
4. 引用内容的URL需要进行安全检查
5. 需要考虑网络中断等异常情况下的处理