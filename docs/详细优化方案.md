# 详细优化方案

## 1. 概述

本文档基于 [优化方案.md](file:///D:/code/chatbot-ui/chatbot/backend/docs/%E6%A6%82%E8%A6%81/%E4%BC%98%E5%8C%96%E6%96%B9%E6%A1%88.md) 提出的优化需求，结合当前项目结构和业务逻辑，制定详细的实施计划。优化内容主要涉及对话列表、消息管理和用户登出功能三个方面。

## 2. 数据库优化

### 2.1 对话表 (conversations) 优化

**当前结构分析**：
- 当前对话表需要添加新字段以支持置顶功能和时间追踪

**优化方案**：
1. 添加 `sticky_flag` 字段（布尔型），用于标记对话是否置顶
2. 添加 `created_at` 和 `updated_at` 时间戳字段
3. 确保每个对话都有唯一标识符

**SQL 修改语句**：
```sql
ALTER TABLE conversations 
ADD COLUMN sticky_flag BOOLEAN DEFAULT FALSE,
ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
```

### 2.2 消息表 (messages) 优化

**当前结构分析**：
- 当前消息表缺少父子消息关系和时间追踪字段

**优化方案**：
1. 添加 `parent_msg_id` 字段（整型），指向父消息ID
2. 添加 `created_at` 和 `updated_at` 时间戳字段
3. 第一条消息的 `parent_msg_id` 设为0

**SQL 修改语句**：
```sql
ALTER TABLE messages 
ADD COLUMN parent_msg_id INTEGER DEFAULT 0,
ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
```

## 3. 后端API优化

### 3.1 对话管理接口优化

#### 3.1.1 获取对话列表接口
- **路径**: `GET /api/conversations`
- **功能增强**:
  1. 返回数据中增加 `sticky_flag` 字段
  2. 添加 `created_at` 和 `updated_at` 时间字段
  3. 置顶对话优先显示

#### 3.1.2 更新对话置顶状态接口
- **路径**: `PUT /api/conversations/{conversation_id}/sticky`
- **参数**:·
  - `sticky_flag`: 布尔值，表示是否置顶
- **功能**: 更新对话的置顶状态并更新 `updated_at` 字段

#### 3.1.3 创建对话接口
- **路径**: `POST /api/conversations/new`
- **功能增强**:
  1. 默认设置 `sticky_flag` 为 False
  2. 设置 `created_at` 和 `updated_at` 为当前时间

### 3.2 消息管理接口优化

#### 3.2.1 获取消息列表接口
- **路径**: `GET /api/messages`
- **功能增强**:
  1. 返回数据中增加 `parent_msg_id` 字段
  2. 添加 `created_at` 和 `updated_at` 时间字段
  3. 根据 `parent_msg_id` 构建消息树状结构

#### 3.2.2 发送消息接口
- **路径**: `POST /api/chat/stream`
- **功能增强**:
  1. 保存消息时设置正确的 `parent_msg_id`
  2. 设置 `created_at` 和 `updated_at` 为当前时间

### 3.3 用户登出接口

#### 3.3.1 登出接口
- **路径**: `POST /api/logout`
- **功能**:
  1. 清除用户的认证状态
  2. 使当前的访问令牌失效
  3. 返回成功响应，前端跳转到登录页面

## 4. 前端优化

### 4.1 对话列表界面优化

#### 4.1.1 对话列表展示

- 根据返回的update_at按时间分组显示对话:
  - 今天
  - 最近一周
  - 最近30天
  - 更早
- 置顶的对话始终显示在最前面

#### 4.1.2 置顶功能
- 点击按钮调用置顶接口更新状态
- 界面实时更新对话顺序

### 4.2 消息展示优化

#### 4.2.1 消息层级展示
- 根据 `parent_msg_id` 构建消息树
- 第一条消息（parent_msg_id=0）左对齐显示
- 回复消息根据发送者角色左右排列
- 添加消息引用线以显示层级关系

### 4.3 用户登出功能

#### 4.3.1 登出按钮
- 在用户信息区域添加登出按钮
- 点击按钮调用登出接口
- 清除本地存储的认证信息
- 跳转到登录页面

## 5. 实施计划

### 5.1 第一阶段：数据库结构优化
1. 修改Conversation模型，添加sticky_flag、created_at、updated_at字段
2. 修改Message模型，添加parent_msg_id、created_at、updated_at字段

### 5.2 第二阶段：后端API实现
1. 更新ConversationDao和MessageDao，支持新字段操作
2. 更新相关Schema，支持新字段传输
3. 实现新的API接口（更新置顶状态、改进的消息和对话查询）

### 5.3 第三阶段：前端界面优化
1. 重构对话列表界面以支持分组和置顶功能
2. 重构消息展示界面以支持层级显示
3. 实现登出功能
4. 优化UI/UX，确保用户体验

### 5.4 第四阶段：测试和优化
1. 性能优化和错误修复

